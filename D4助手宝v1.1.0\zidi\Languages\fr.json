{"About": "À propos", "AccentColor": "Accent Color", "Add": "Add", "AlwaysOnTop": "Toujours au dessus", "Audio": "Audio", "AudioFormat": "Audio Format", "AudioSaved": "Audio enregistré", "BackColor": "<PERSON><PERSON><PERSON> de fond", "BorderColor": "<PERSON><PERSON>ur du bord", "BorderThickness": "Epaisseur du bord", "Bottom": "<PERSON><PERSON><PERSON>", "CaptureDuration": "<PERSON><PERSON><PERSON> de <PERSON> capture (en secondes)", "Center": "Centre", "Changelog": "Log de changements", "Clear": "<PERSON><PERSON><PERSON><PERSON>", "ClearRecentList": "Effacer la liste récente", "Clipboard": "Presse-papier", "Close": "<PERSON><PERSON><PERSON>", "Color": "<PERSON><PERSON><PERSON>", "ConfigCodecs": "Configure Codecs", "Configure": "Configurer", "CopyOutPathClipboard": "<PERSON><PERSON><PERSON> le chemin du fichier sortie vers le presse-papier", "CopyPath": "<PERSON><PERSON><PERSON> le chemin", "CopyToClipboard": "Co<PERSON>r vers le presse-papier", "CornerRadius": "Rayon du coin", "CrashLogs": "Crash Logs", "Crop": "Crop", "CustomSize": "Custom Size", "CustomUrl": "Custom Url", "DarkTheme": "Dark Theme", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "DiscardChanges": "Discard Changes", "Disk": "Disque", "Donate": "<PERSON><PERSON>", "DownloadFFmpeg": "Download FFmpeg", "Edit": "Edit", "Elapsed": "Elapsed", "ErrorOccurred": "Une erreur s'est produite", "Exit": "<PERSON><PERSON><PERSON>", "FFmpegFolder": "Dossier FFmpeg", "FFmpegLog": "Log FFmpeg", "FileMenu": "File", "FileMenuNew": "New", "FileMenuOpen": "Open", "FileMenuSave": "Save", "FileNaming": "File Naming", "Flip": "<PERSON><PERSON><PERSON>", "FontSize": "Taille de la police", "FrameRate": "FPS", "FullScreen": "Plein écran", "HideOnFullScreenShot": "<PERSON><PERSON> lors d'une capture d'é<PERSON>ran complète", "Horizontal": "Horizontal", "Host": "<PERSON><PERSON><PERSON>", "Hotkeys": "<PERSON><PERSON><PERSON><PERSON>", "ImageEditor": "Image Editor", "ImgEmpty": "Non enregistré. L'image prise était vide", "ImgFormat": "Format d'image", "ImgSavedClipboard": "Image enregistrée dans le presse-papier", "ImgurFailed": "Le téléchargement vers Imgur a échoué", "ImgurSuccess": "Le téléchargment vers Imgur a réussi", "ImgurUploading": "Téléchargement vers Imgur en cours", "IncludeClicks": "<PERSON><PERSON><PERSON> les clics de souris", "IncludeCursor": "<PERSON><PERSON><PERSON> le curseur", "IncludeKeys": "Inclure les entrées clavier", "Keymap": "Keymap", "Keystrokes": "<PERSON><PERSON><PERSON> clavier", "KeystrokesHistoryCount": "History Count", "KeystrokesHistorySpacing": "History Spacing", "KeystrokesSeparateFile": "Save to separate Text file", "Language": "<PERSON><PERSON>", "Left": "G<PERSON><PERSON>", "LoopbackSource": "Source de la sortie enceintes", "MaxRecent": "Maximum d'items persistants", "MaxTextLength": "Taille maximum du texte", "MicSource": "Source du microphone", "MinCapture": "Miminimiser au début de la capture", "Minimize": "Minimiser", "MinTray": "Minimiser dans la zone de notification", "MinTrayStartup": "Minimize to Tray on Startup", "MinTrayClose": "Minimize to Tray when Closed", "MouseClicks": "<PERSON><PERSON><PERSON> de <PERSON> souris", "MouseMiddleClickColor": "Middle Click Color", "MousePointer": "<PERSON>", "MouseRightClickColor": "Right Click Color", "NewWindow": "NewWindow", "No": "Non", "None": "None", "Notifications": "Notifications", "NotSaved": "Non enregistré", "NoWebcam": "Pas de webcam", "Ok": "OK", "OnlyAudio": "Audio uniquement", "Opacity": "Opacité", "OpenFromClipboard": "Open from Clipboard", "OpenOutFolder": "<PERSON><PERSON><PERSON><PERSON><PERSON> le dossier de sortie", "OutFolder": "<PERSON><PERSON>r de sortie", "Overlays": "Overlays", "Padding": "Padding", "Password": "Mot de passe", "Paused": "Enregistrement mis en pause", "PauseResume": "Pause | Reprendre", "PauseResumeRecording": "Mettre en pause/Reprendre l'enregistrement", "PlayRecAudio": "Playback recorded audio in real-time", "Port": "Port", "Preview": "Preview", "PreStartCountdown": "Pre Start Countdown (seconds)", "Proxy": "Proxy", "Quality": "Qualité", "Radius": "Rayon", "Recent": "<PERSON><PERSON><PERSON>", "RecordStop": "Enregistrer | Stop", "Redo": "Redo", "Refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Region": "Région", "RegionSelector": "Sélecteur de région", "RemoveFromList": "<PERSON><PERSON><PERSON> de la liste", "Reset": "<PERSON><PERSON><PERSON><PERSON>", "Resize": "Reformater", "RestoreDefaults": "<PERSON><PERSON>", "Right": "<PERSON><PERSON><PERSON>", "Rotate": "<PERSON><PERSON>", "SaveToClipboard": "Save to Clipboard", "Screen": "Ecran", "ScreenShot": "Capture d'écran", "ScreenShotActiveWindow": "Capturer la fenêtre active", "ScreenShotDesktop": "Capturer le bureau", "ScreenShotSaved": "Capture d'écran enregistrée", "ScreenShotTransforms": "Transformations de capture d'écran", "SelectFFmpegFolder": "Sélectionner le dossier FFmpeg", "SelectOutFolder": "Sélectionner le dossier de sortie", "SeparateAudioFiles": "Separate files for every audio source", "ShowSysNotify": "Montrer les notifications", "SnapToWindow": "Snap to Window", "Sounds": "Sounds", "StartStopRecording": "Commencer/Arrêter l'enregistrement", "StreamingKeys": "Streaming Keys", "Timeout": "<PERSON><PERSON><PERSON> maximum", "ToggleMouseClicks": "Toggle Mouse Clicks", "ToggleKeystrokes": "Toggle Keystrokes", "Tools": "Tools", "Top": "<PERSON><PERSON>", "TrayIcon": "Tray Icon", "Trim": "<PERSON><PERSON>", "Undo": "Undo", "UploadToImgur": "Upload to Imgur", "UseProxyAuth": "Utiliser un proxy d'authentification", "UserName": "Nom d'utilisateur", "VarFrameRate": "Taux d'image variable", "Vertical": "Vertical", "Video": "Vidéo", "VideoEncoder": "Encodeur vidéo", "VideoSaved": "Video enregistrée", "VideoSource": "Source vidéo", "ViewCrashLogs": "View Crash Logs", "ViewLicenses": "View Licenses", "ViewOnGitHub": "View on GitHub", "WantToTranslate": "Voulez vous traduire?", "WebCam": "Webcam", "WebCamSeparateFile": "Record Webcam to separate file", "WebCamView": "Vue de la Webcam", "Website": "Website", "Window": "<PERSON><PERSON><PERSON>", "Yes": "O<PERSON>"}