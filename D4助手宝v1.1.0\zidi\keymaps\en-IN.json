{"$schema": "./schema.json", "Name": "English (India)", "Mappings": [{"On": [{}], "Keys": {"Control": "Ctrl", "ControlKey": "Ctrl", "LControlKey": "Ctrl", "RControlKey": "Ctrl", "Alt": "Alt", "Menu": "Alt", "LMenu": "Alt", "RMenu": "Alt", "Shift": "Shift", "ShiftKey": "Shift", "LShiftKey": "Shift", "RShiftKey": "Shift", "LWin": "Win", "RWin": "Win", "D0": "0", "D1": "1", "D2": "2", "D3": "3", "D4": "4", "D5": "5", "D6": "6", "D7": "7", "D8": "8", "D9": "9", "NumPad0": "0", "NumPad1": "1", "NumPad2": "2", "NumPad3": "3", "NumPad4": "4", "NumPad5": "5", "NumPad6": "6", "NumPad7": "7", "NumPad8": "8", "NumPad9": "9", "Escape": "Esc", "Back": "Backspace", "PageUp": "Pg Up", "PageDown": "Pg Dn", "Left": "Left", "Right": "Right", "Up": "Up", "Down": "Down", "PrintScreen": "Prt Sc", "Insert": "Ins", "Delete": "Del", "VolumeDown": "Vol -", "VolumeUp": "Vol +", "VolumeMute": "Mute", "BrowserBack": "Browser Back", "BrowserForward": "Browser Forward", "BrowserRefresh": "Browser Refresh", "BrowserStop": "Browser Stop", "BrowserSearch": "Browser Search", "BrowserFavorites": "Browser Favourites", "BrowserHome": "Browser Home", "MediaNextTrack": "Next Track", "MediaPreviousTrack": "Prev Track", "MediaStop": "Stop", "MediaPlayPause": "Play / Pause", "SelectMedia": "Select Media", "LaunchMail": "Launch Mail", "Multiply": "*", "Add": "+", "Subtract": "-", "Divide": "/", "Decimal": ".", "NumLock": "NumLock", "Oemtilde": "`", "OemMinus": "-", "Oemplus": "=", "OemCloseBrackets": "]", "OemOpenBrackets": "[", "OemPipe": "\\", "OemQuotes": "'", "OemSemicolon": ";", "OemPeriod": ".", "Oemcomma": ",", "OemQuestion": "/"}}, {"On": [{"Shift": true}], "Keys": {"D0": ")", "D1": "!", "D2": "@", "D3": "#", "D4": "$", "D5": "%", "D6": "^", "D7": "&", "D8": "*", "D9": "(", "Oemtilde": "~", "OemMinus": "_", "Oemplus": "+", "OemCloseBrackets": "}", "OemOpenBrackets": "{", "OemPipe": "|", "OemQuotes": "\"", "OemSemicolon": ":", "OemPeriod": ">", "Oemcomma": "<", "OemQuestion": "?"}}, {"On": [{"Shift": true}, {"CapsLock": true}], "Keys": {"A": "A", "B": "B", "C": "C", "D": "D", "E": "E", "F": "F", "G": "G", "H": "H", "I": "I", "J": "J", "K": "K", "L": "L", "M": "M", "N": "N", "O": "O", "P": "P", "Q": "Q", "R": "R", "S": "S", "T": "T", "U": "U", "V": "V", "W": "W", "X": "X", "Y": "Y", "Z": "Z"}}, {"On": [{}, {"CapsLock": true, "Shift": true}], "Keys": {"A": "a", "B": "b", "C": "c", "D": "d", "E": "e", "F": "f", "G": "g", "H": "h", "I": "i", "J": "j", "K": "k", "L": "l", "M": "m", "N": "n", "O": "o", "P": "p", "Q": "q", "R": "r", "S": "s", "T": "t", "U": "u", "V": "v", "W": "w", "X": "x", "Y": "y", "Z": "z"}}, {"On": [{"Control": true, "Shift": true}, {"Control": true, "Alt": true}], "Keys": {"D4": "₹"}}, {"On": [{"Control": true, "Alt": true}, {"Control": true, "Alt": true, "Shift": true, "CapsLock": true}], "Keys": {"Q": "æ", "E": "ē", "R": "r̥", "T": "ṭ", "Y": "ñ", "U": "ū", "I": "ī", "O": "<PERSON>", "A": "ā", "S": "ś", "D": "ḍ", "G": "ṅ", "H": "ḥ", "L": "l̥", "X": "ṣ", "N": "ṇ", "M": "ṁ"}}, {"On": [{"Control": true, "Alt": true, "Shift": true}, {"Control": true, "Alt": true, "CapsLock": true}], "Keys": {"Q": "<PERSON>", "E": "Ē", "R": "R̥", "T": "Ṭ", "Y": "Ñ", "U": "Ū", "I": "Ī", "O": "Ō", "A": "Ā", "S": "Ś", "D": "Ḍ", "G": "Ṅ", "H": "Ḥ", "L": "L̥", "X": "Ṣ", "N": "Ṇ", "M": "Ṁ"}}]}