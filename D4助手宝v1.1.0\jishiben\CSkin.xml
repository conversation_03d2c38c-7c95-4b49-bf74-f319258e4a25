<?xml version="1.0"?>
<doc>
    <assembly>
        <name><PERSON>kin</name>
    </assembly>
    <members>
        <member name="F:CCWin.SkinForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinForm.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CCWin.SkinMain.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinMain.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinMain.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:CCWin.SkinMain.Special">
            <summary>
            是否启用窗口淡入淡出
            </summary>
        </member>
        <member name="P:CCWin.SkinMain.Opacity">
            <summary>
            设置窗体的不透明度
            </summary>
        </member>
        <member name="M:CCWin.CCSkinMain.AllSysButtonWidth(System.Boolean)">
            <summary>
            所以自定义系统按钮的总宽度
            </summary>
            <param name="space">宽度是否包括ControlBoxSpace间隔值</param>
            <returns>总宽度</returns>
        </member>
        <member name="M:CCWin.CCSkinMain.AllButtonWidth(System.Boolean)">
            <summary>
            所有系统按钮的总宽度
            </summary>
            <param name="space">宽度是否包括ControlBoxSpace间隔值</param>
            <returns>总宽度</returns>
        </member>
        <member name="M:CCWin.CCSkinMain.main_ThemeChanged(System.Object,CCWin.ThemeEventArgs)">
            <summary>
            主题变换时
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.CCSkinMain.main_BackChanged(System.Object,CCWin.BackEventArgs)">
            <summary>
            背景变换时
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.CCSkinMain.main_BackgroundImageChanged(System.Object,System.EventArgs)">
            <summary>
            背景变换时
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.CCSkinMain.MoveForm">
            <summary>
            移动窗体
            </summary>
        </member>
        <member name="M:CCWin.CCSkinMain.DrawCaptionBackground(System.Drawing.Graphics)">
            <summary>
            画标题栏背景
            </summary>
            <param name="g"></param>
        </member>
        <member name="M:CCWin.CCSkinMain.ResizeCore">
            <summary>
            窗体改变大小。
            </summary>
        </member>
        <member name="M:CCWin.CCSkinMain.WmWindowPosChanged(System.Windows.Forms.Message@)">
            <summary>
            响应 WM_WINDOWPOSCHANGED 消息。
            </summary>
            <param name="m"></param>
        </member>
        <member name="M:CCWin.CCSkinMain.WmNcRButtonUp(System.Windows.Forms.Message@)">
            <summary>
            响应 WM_NCRBUTTONUP 消息。
            </summary>
            <param name="m"></param>
        </member>
        <member name="M:CCWin.CCSkinMain.WmNcCalcSize(System.Windows.Forms.Message@)">
            <summary>
            响应 WM_NCCALCSIZE 消息。
            </summary>
            <param name="m"></param>
        </member>
        <member name="M:CCWin.CCSkinMain.IsAboutToMaximize(CCWin.Win32.WinAPI.RECT)">
            <summary>
            判断所接收到的 wm_nc-calc-size 消息是否指示窗体即将最大化
            </summary>        
        </member>
        <member name="M:CCWin.CCSkinMain.SetClientSizeCore(System.Int32,System.Int32)">
            <summary>
            重写该方法解决在VS设计器中，每次保存一个新的尺寸，再打开尺寸会变大的问题
            </summary>
        </member>
        <member name="M:CCWin.CCSkinMain.SetBoundsCore(System.Int32,System.Int32,System.Int32,System.Int32,System.Windows.Forms.BoundsSpecified)">
            <summary>
            重写该方法解决窗体每次还原都会变大的问题
            </summary> 
        </member>
        <member name="M:CCWin.CCSkinMain.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.CaptionBackColorTop">
            <summary>
            标题栏颜色是从上到下渐变的，这个值设置上边的颜色值
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.CaptionBackColorBottom">
            <summary>
            标题栏颜色是从上到下渐变的，这个值设置下边的颜色值
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MdiStretchImage">
            <summary>
            Mdi背景拉伸绘制
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MdiImageAlign">
            <summary>
            Mdi背景绘制位置
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MdiImage">
            <summary>
            Mdi背景图像
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MdiAutoScroll">
            <summary>
            是否显示Mdi滚动条
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MdiBorderStyle">
            <summary>
            Mdi边框样式
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MdiBackColor">
            <summary>
            MDI容器背景色
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MdiClientController">
            <summary>
            MDI容器对象
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.XTheme">
            <summary>
            窗体主题
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.SysButtonItems">
            <summary>
            获取自定义系统按钮的集合
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.InheritBack">
            <summary>
            是否继承所属窗体的背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.InheritTheme">
            <summary>
            是否继承所属窗体的背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.BorderPadding">
            <summary>
            获取或设置窗体的边框大小。
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.SkinOpacity">
            <summary>
            窗体渐变后透明度
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.Back">
            <summary>
            背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MobileApi">
            <summary>
            窗体移动是否调用Api
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.BackLayout">
            <summary>
            是否从左绘制背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.BackPalace">
            <summary>
            质感层背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.BorderPalace">
            <summary>
            边框层背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.ShowBorder">
            <summary>
            是否在窗体上绘画边框
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.ShowDrawIcon">
            <summary>
            是否在窗体上绘画ICO图标
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.Special">
            <summary>
            是否启用窗口淡入淡出
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.Shadow">
            <summary>
            是否启用窗体阴影
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.IsShadowStraight">
            <summary>
            是否是直角样式的阴影
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.ShadowColor">
            <summary>
            窗体阴影颜色
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.ShadowWidth">
            <summary>
            窗体阴影宽度
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.ShadowPalace">
            <summary>
            阴影边框图
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.ShadowRectangle">
            <summary>
            阴影九宫绘画区域
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.BackRectangle">
            <summary>
            质感层九宫绘画区域
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.BorderRectangle">
            <summary>
            边框质感层九宫绘画区域
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.BackToColor">
            <summary>
            是否根据背景图决定背景色
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.TitleCenter">
            <summary>
            标题是否居中
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.EffectBack">
            <summary>
            发光字体背景色
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.TitleColor">
            <summary>
            标题颜色
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.TitleOffset">
            <summary>
            设置或获取标题的偏移
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.EffectWidth">
            <summary>
            光圈大小
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MiniMouseBack">
            <summary>
            最小化按钮悬浮时
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MiniDownBack">
            <summary>
            最小化按钮点击时
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MiniNormlBack">
            <summary>
            最小化按钮初始时
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MaxMouseBack">
            <summary>
            最大化按钮悬浮时背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MaxDownBack">
            <summary>
            最大化按钮点击时背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.MaxNormlBack">
            <summary>
            最大化按钮初始时背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.RestoreMouseBack">
            <summary>
            还原按钮悬浮时背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.RestoreDownBack">
            <summary>
            还原按钮点击时背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.RestoreNormlBack">
            <summary>
            还原按钮初始时背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.CloseMouseBack">
            <summary>
            关闭按钮悬浮时背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.CloseDownBack">
            <summary>
            关闭按钮点击时背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.CloseNormlBack">
            <summary>
            关闭按钮初始时背景
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.ShowSystemMenu">
            <summary>
            获取或设置窗体是否显示系统菜单。
            </summary>
        </member>
        <member name="P:CCWin.CCSkinMain.RealClientRect">
            <summary>
            获取窗体的真实客户区大小。
            </summary>
        </member>
        <member name="F:CCWin.Skin_Color.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.Skin_Color.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.Skin_Color.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CCWin.Skin_VS.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.Skin_VS.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.Skin_VS.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CCWin.ThemeEventArgs.#ctor(CCWin.CCSkinMain)">
            <summary>
            XTheme主题属性值更改时引发的事件
            </summary>
            <param name="afterTheme">更改后主题</param>
        </member>
        <member name="M:CCWin.BackEventArgs.#ctor(System.Drawing.Image,System.Drawing.Image)">
            <summary>
            Back属性值更改时引发的事件
            </summary>
            <param name="beforeBack">更改前背景</param>
            <param name="afterBack">更改后背景</param>
        </member>
        <member name="M:CCWin.CCSkinForm.CanPenetrate">
            <summary>
            使窗口有鼠标穿透功能
            </summary>
        </member>
        <member name="F:CCWin.CCSkinForm.ShadowColors">
            <summary>
            四边阴影的颜色。[0]为阴影内沿颜色，[1]为阴影外沿颜色
            </summary>
        </member>
        <member name="F:CCWin.CCSkinForm.CornerColors">
            <summary>
            圆角阴影的颜色。[0]为阴影内沿颜色，[1]为阴影外沿颜色。
            注：一般来讲，圆角阴影内沿的颜色应当比四边阴影内沿的颜色更深，才会有更好的显示效果。此值应当根据您的实际情况而定。
            </summary>
            <remarks>由于给扇面上渐变时，起点并不是准确的扇面内弧，因此扇面的内沿颜色可能应比四边的内沿颜色深</remarks>
        </member>
        <member name="M:CCWin.CCSkinForm.DrawShadow(System.Drawing.Graphics)">
            <summary>
            绘制四角、四边的阴影
            </summary>
            <param name="g"></param>
        </member>
        <member name="M:CCWin.CCSkinForm.DrawCorners(System.Drawing.Graphics,System.Drawing.Size)">
            <summary>
            绘制四角的阴影
            </summary>
            <param name="g"></param>
            <param name="corSize">圆角区域正方形的大小</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.CCSkinForm.DrawLines(System.Drawing.Graphics,System.Drawing.Size,System.Drawing.Size,System.Drawing.Size)">
            <summary>
            绘制上下左右四边的阴影
            </summary>
            <param name="g"></param>
            <param name="corSize"></param>
            <param name="gradientSize_LR"></param>
            <param name="gradientSize_TB"></param>
        </member>
        <member name="F:CCWin.CCSkinForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.CCSkinForm.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.CCSkinForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CCWin.CmSysButton.Clone">
            <summary>
            深拷贝
            </summary>
            <returns>深度克隆的自定义系统按钮</returns>
        </member>
        <member name="P:CCWin.CmSysButton.Name">
            <summary>
            与对象关联的用户定义数据
            </summary>
        </member>
        <member name="P:CCWin.CmSysButton.BoxState">
            <summary>
            按钮的状态
            </summary>
        </member>
        <member name="P:CCWin.CmSysButton.Bounds">
            <summary>
            获取或设置自定义系统按钮的显示区域
            </summary>
        </member>
        <member name="P:CCWin.CmSysButton.Location">
            <summary>
            按钮的位置
            </summary>
        </member>
        <member name="P:CCWin.CmSysButton.Size">
            <summary>
            按钮的大小
            </summary>
        </member>
        <member name="P:CCWin.CmSysButton.ToolTip">
            <summary>
            自定义系统按钮悬浮提示
            </summary>
        </member>
        <member name="P:CCWin.CmSysButton.Visibale">
            <summary>
            自定义系统按钮是否显示
            </summary>
        </member>
        <member name="P:CCWin.CmSysButton.SysButtonMouse">
            <summary>
            自定义系统按钮悬浮时
            </summary>
        </member>
        <member name="P:CCWin.CmSysButton.SysButtonDown">
            <summary>
            自定义系统按钮点击时
            </summary>
        </member>
        <member name="P:CCWin.CmSysButton.SysButtonNorml">
            <summary>
            自定义系统按钮初始时
            </summary>
        </member>
        <member name="P:CCWin.CmSysButton.OwnerForm">
            <summary>
            获取自定义系统按钮所在的窗体
            </summary>
        </member>
        <member name="M:CCWin.CustomSysButtonCollection.IndexOf(CCWin.CmSysButton)">
            <summary>
            获取自定义系统按钮所在的索引位置
            </summary>
            <param name="item">要获取的自定义系统按钮</param>
            <returns>索引位置</returns>
        </member>
        <member name="M:CCWin.CustomSysButtonCollection.Add(CCWin.CmSysButton)">
            <summary>
            添加一个自定义系统按钮
            </summary>
            <param name="item">要添加的自定义系统按钮</param>
        </member>
        <member name="M:CCWin.CustomSysButtonCollection.AddRange(CCWin.CmSysButton[])">
            <summary>
            添加一个列表项的自定义系统按钮
            </summary>
            <param name="items">要添加的自定义系统按钮的数组</param>
        </member>
        <member name="M:CCWin.CustomSysButtonCollection.Remove(CCWin.CmSysButton)">
            <summary>
            移除一个自定义系统按钮
            </summary>
            <param name="item">要移除的自定义系统按钮</param>
        </member>
        <member name="M:CCWin.CustomSysButtonCollection.RemoveAt(System.Int32)">
            <summary>
            根据索引位置删除一个自定义系统按钮
            </summary>
            <param name="index">索引位置</param>
        </member>
        <member name="M:CCWin.CustomSysButtonCollection.Clear">
            <summary>
            清空所有列表项
            </summary>
        </member>
        <member name="M:CCWin.CustomSysButtonCollection.Insert(System.Int32,CCWin.CmSysButton)">
            <summary>
            根据索引位置插入一个自定义系统按钮
            </summary>
            <param name="index">索引位置</param>
            <param name="item">要插入的自定义系统按钮</param>
        </member>
        <member name="M:CCWin.CustomSysButtonCollection.Contains(CCWin.CmSysButton)">
            <summary>
            判断一个自定义系统按钮是否在集合内
            </summary>
            <param name="item">要判断的自定义系统按钮</param>
            <returns>是否在自定义系统按钮集合内</returns>
        </member>
        <member name="M:CCWin.CustomSysButtonCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            将自定义系统按钮的集合拷贝至一个数组
            </summary>
            <param name="array">目标数组</param>
            <param name="index">拷贝的索引位置</param>
        </member>
        <member name="P:CCWin.CustomSysButtonCollection.Item(System.Int32)">
            <summary>
            根据索引获取一个自定义系统按钮
            </summary>
            <param name="index">索引位置</param>
            <returns>自定义系统按钮</returns>
        </member>
        <member name="M:CCWin.ImageDrawRect.DrawRect(System.Drawing.Graphics,System.Drawing.Bitmap,System.Drawing.Rectangle,System.Drawing.Rectangle,System.Int32,System.Int32)">
            <summary>
            绘图对像
            </summary>
            <param name="g">绘图对像</param>
            <param name="img">图片</param>
            <param name="r">绘置的图片大小、坐标</param>
            <param name="lr">绘置的图片边界</param>
            <param name="index">当前状态</param> 
            <param name="Totalindex">状态总数</param>
        </member>
        <member name="M:CCWin.ImageDrawRect.DrawRect(System.Drawing.Graphics,System.Drawing.Bitmap,System.Drawing.Rectangle,System.Int32,System.Int32)">
            <summary>
            绘图对像
            </summary>
            <param name="g"> 绘图对像</param>
            <param name="obj">图片对像</param>
            <param name="r">绘置的图片大小、坐标</param>
            <param name="index">当前状态</param>
            <param name="Totalindex">状态总数</param>
        </member>
        <member name="F:CCWin.Skin_DevExpress.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.Skin_DevExpress.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.Skin_DevExpress.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CCWin.FrmPrintscreen.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.FrmPrintscreen.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.FrmPrintscreen.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:CCWin.SkinClass.SkinButtonState">
            <summary>
            定义三态按钮的各个状态, 因为系统也有一个ButtonState类型，
            为避免冲突，加了GM前缀
            </summary>
        </member>
        <member name="T:CCWin.SkinClass.ButtonBorderType">
            <summary>
            定义按钮的边框是方形的还是圆形的
            </summary>
        </member>
        <member name="M:CCWin.SkinClass.DrawImage.ImageFillRect(System.Drawing.Bitmap,System.Drawing.Bitmap,System.Windows.Forms.ImageLayout)">
            <summary>
            将指向图像按指定的填充模式绘制到目标图像上
            </summary>
            <param name="SourceBmp">要控制填充模式的源图</param>
            <param name="TargetBmp">要绘制到的目标图</param>
            <param name="_FillMode">填充模式</param>
            <remarks></remarks>
        </member>
        <member name="T:CCWin.SkinClass.HttpHelper">
            <summary>  
            有关HTTP请求的辅助类  
            </summary>  
        </member>
        <member name="M:CCWin.SkinClass.HttpHelper.CreateGetHttpResponse(System.String,System.Nullable{System.Int32},System.String,System.Net.CookieCollection)">
            <summary>  
            创建GET方式的HTTP请求  
            </summary>  
            <param name="url">请求的URL</param>  
            <param name="timeout">请求的超时时间</param>  
            <param name="userAgent">请求的客户端浏览器信息，可以为空</param>  
            <param name="cookies">随同HTTP请求发送的Cookie信息，如果不需要身份验证可以为空</param>  
            <returns></returns>  
        </member>
        <member name="M:CCWin.SkinClass.HttpHelper.CreatePostHttpResponse(System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.Nullable{System.Int32},System.String,System.Text.Encoding,System.Net.CookieContainer)">
            <summary>  
            创建POST方式的HTTP请求  
            </summary>  
            <param name="url">请求的URL</param>  
            <param name="parameters">随同请求POST的参数名称及参数值字典</param>  
            <param name="timeout">请求的超时时间</param>  
            <param name="userAgent">请求的客户端浏览器信息，可以为空</param>  
            <param name="requestEncoding">发送HTTP请求时所用的编码</param>  
            <param name="cookies">随同HTTP请求发送的Cookie信息，如果不需要身份验证可以为空</param>  
            <returns></returns>  
        </member>
        <member name="M:CCWin.SkinClass.HttpHelper.GetUrlImg(System.String)">
            <summary>
            根据URL地址加载Image
            </summary>
            <param name="ImgUrl">图片url</param>
            <returns>Image图片</returns>
        </member>
        <member name="F:CCWin.Skin_Mac.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.Skin_Mac.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.Skin_Mac.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CCWin.Skin_Metro.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.Skin_Metro.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.Skin_Metro.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CCWin.Skin_Metro.InternalBorderColor">
            <summary>
            内部边框颜色
            </summary>
        </member>
        <member name="P:CCWin.Skin_Metro.InternalBackColor">
            <summary>
            内部背景颜色
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.SysButtonEventArgs">
            <summary>
            自定义按钮事件参数
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SysButtonEventArgs.#ctor(CCWin.CmSysButton)">
            <summary>
            带参构造
            </summary>
            <param name="sysButton">所点击的自定义系统按钮</param>
        </member>
        <member name="P:CCWin.SkinControl.SysButtonEventArgs.SysButton">
            <summary>
            所点击的自定义系统按钮
            </summary>
        </member>
        <member name="T:CCWin.MessageBoxForm">
            <summary>
            实现用于消息框对话框的窗体。
            </summary>
        </member>
        <member name="M:CCWin.MessageBoxForm.#ctor">
            <summary>
            初始化 <see cref="T:CCWin.MessageBoxForm"/> 类的新实例。
            </summary>
        </member>
        <member name="M:CCWin.MessageBoxForm.ShowMessageBoxDialog(CCWin.MessageBoxArgs)">
            <summary>
            使用 <see cref="T:CCWin.MessageBoxArgs"/> 消息对话框参数显示窗体。
            </summary>
            <param name="message"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.MessageBoxForm.DoShowDialog(System.Windows.Forms.IWin32Window)">
            <summary>
            显示消息对话框。
            </summary>
            <param name="owner">任何实现 <see cref="T:System.Windows.Forms.IWin32Window"/>（表示将拥有模式对话框的顶级窗口）的对象。</param>
            <returns><see cref="T:System.Windows.Forms.DialogResult"/> 值之一。</returns>
        </member>
        <member name="P:CCWin.MessageBoxForm.Message">
            <summary>
            获取消息对话框的参数对象。
            </summary>
        </member>
        <member name="P:CCWin.MessageBoxForm.MessageRect">
            <summary>
            获取消息文本的位置与大小。
            </summary>
        </member>
        <member name="P:CCWin.MessageBoxForm.IconRect">
            <summary>
            获取消息图标的位置与大小。
            </summary>
        </member>
        <member name="T:CCWin.MessageBoxArgs">
            <summary>
            消息框对话消息参数。
            </summary>
        </member>
        <member name="M:CCWin.MessageBoxArgs.#ctor">
            <summary>
            初始化 <see cref="T:CCWin.MessageBoxArgs"/> 类的新实例。
            </summary>
        </member>
        <member name="M:CCWin.MessageBoxArgs.#ctor(System.Windows.Forms.IWin32Window,System.String,System.String,System.Windows.Forms.MessageBoxButtons,System.Drawing.Icon,System.Windows.Forms.MessageBoxDefaultButton)">
            <summary>
             初始化 <see cref="T:CCWin.MessageBoxArgs"/> 类的新实例。
            </summary>
            <param name="owner">任何实现 <see cref="T:System.Windows.Forms.IWin32Window"/>（表示将拥有模式对话框的顶级窗口）的对象。</param>
            <param name="text">需要显示消息文本。</param>
            <param name="caption">需要现实的消息标题。</param>
            <param name="buttons"><see cref="T:System.Windows.Forms.MessageBoxButtons"/> 值之一。</param>
            <param name="icon">需要显示的消息图标。</param>
            <param name="defaultButton"><see cref="T:System.Windows.Forms.MessageBoxDefaultButton"/> 值之一。</param>
        </member>
        <member name="T:CCWin.SkinClass.ControlState">
            <summary>
            控件的状态。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.ControlState.Normal">
            <summary>
             正常。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.ControlState.Hover">
            <summary>
            鼠标进入。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.ControlState.Pressed">
            <summary>
            鼠标按下。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.ControlState.Focused">
            <summary>
            获得焦点。
            </summary>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ConvertBase(System.String,System.Int32,System.Int32)">
            <summary>
            实现各进制数间的转换。ConvertBase("15",10,16)表示将十进制数15转换为16进制的数。
            </summary>
            <param name="value">要转换的值,即原值</param>
            <param name="from">原值的进制,只能是2,8,10,16四个值。</param>
            <param name="to">要转换到的目标进制，只能是2,8,10,16四个值。</param>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.isBaseNumber(System.Int32)">
            <summary>
            判断是否是  2 8 10 16
            </summary>
            <param name="baseNumber"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToString(System.Object)">
            <summary>
            异常返回 "" 
            </summary>
            <param name="Value"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToString(System.Object,System.String)">
            <summary>
            异常返回参数outValue
            </summary>
            <param name="Value"></param>
            <param name="outValue"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.StringToBytes(System.String)">
            <summary>
            将string转换成byte[]
            </summary>
            <param name="text">要转换的字符串</param>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.StringToBytes(System.String,System.Text.Encoding)">
            <summary>
            使用指定字符集将string转换成byte[]
            </summary>
            <param name="text">要转换的字符串</param>
            <param name="encoding">字符编码</param>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.BytesToString(System.Byte[])">
            <summary>
            将byte[]转换成string
            </summary>
            <param name="bytes">要转换的字节数组</param>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.BytesToString(System.Byte[],System.Text.Encoding)">
            <summary>
            使用指定字符集将byte[]转换成string
            </summary>
            <param name="bytes">要转换的字节数组</param>
            <param name="encoding">字符编码</param>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.BytesToInt32(System.Byte[])">
            <summary>
            将byte[]转换成int
            </summary>
            <param name="data">需要转换成整数的byte数组</param>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToInt32``1(``0,System.Int32)">
            <summary>
            将数据转换为整型   转换失败返回默认值
            </summary>
            <typeparam name="T">数据类型</typeparam>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToInt32(System.Object)">
            <summary>
            异常返回0
            </summary>
            <param name="Value"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToInt32(System.String,System.Int32)">
            <summary>
            将数据转换为整型   转换失败返回默认值
            </summary>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToInt32(System.Object,System.Int32)">
            <summary>
            将数据转换为整型  转换失败返回默认值
            </summary>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToBoolean``1(``0,System.Boolean)">
            <summary>
            将数据转换为布尔类型  转换失败返回默认值
            </summary>
            <typeparam name="T">数据类型</typeparam>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToBoolean(System.String,System.Boolean)">
            <summary>
            将数据转换为布尔类型  转换失败返回 默认值
            </summary>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToBoolean(System.Object,System.Boolean)">
            <summary>
            将数据转换为布尔类型  转换失败返回 默认值
            </summary>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToFloat``1(``0,System.Single)">
            <summary>
            将数据转换为单精度浮点型  转换失败 返回默认值
            </summary>
            <typeparam name="T">数据类型</typeparam>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToFloat(System.Object,System.Single)">
            <summary>
            将数据转换为单精度浮点型   转换失败返回默认值
            </summary>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToFloat(System.String,System.Single)">
            <summary>
            将数据转换为单精度浮点型   转换失败返回默认值
            </summary>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToDouble``1(``0,System.Double)">
            <summary>
            将数据转换为双精度浮点型   转换失败返回默认值
            </summary>
            <typeparam name="T">数据的类型</typeparam>
            <param name="data">要转换的数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToDouble``1(``0,System.Int32,System.Double)">
            <summary>
            将数据转换为双精度浮点型,并设置小数位   转换失败返回默认值
            </summary>
            <typeparam name="T">数据的类型</typeparam>
            <param name="data">要转换的数据</param>
            <param name="decimals">小数的位数</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToDouble(System.Object,System.Double)">
            <summary>
            将数据转换为双精度浮点型  转换失败返回默认值
            </summary>
            <param name="data">要转换的数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToDouble(System.String,System.Double)">
            <summary>
            将数据转换为双精度浮点型  转换失败返回默认值
            </summary>
            <param name="data">要转换的数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToDouble(System.Object,System.Int32,System.Double)">
            <summary>
            将数据转换为双精度浮点型,并设置小数位  转换失败返回默认值
            </summary>
            <param name="data">要转换的数据</param>
            <param name="decimals">小数的位数</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToDouble(System.String,System.Int32,System.Double)">
            <summary>
            将数据转换为双精度浮点型,并设置小数位  转换失败返回默认值
            </summary>
            <param name="data">要转换的数据</param>
            <param name="decimals">小数的位数</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ConvertTo(System.Object,System.Type)">
            <summary>
            将数据转换为指定类型
            </summary>
            <param name="data">转换的数据</param>
            <param name="targetType">转换的目标类型</param>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ConvertTo``1(System.Object)">
            <summary>
            将数据转换为指定类型
            </summary>
            <typeparam name="T">转换的目标类型</typeparam>
            <param name="data">转换的数据</param>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToDecimal``1(``0,System.Decimal)">
            <summary>
            将数据转换为Decimal  转换失败返回默认值
            </summary>
            <typeparam name="T">数据类型</typeparam>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToDecimal(System.Object,System.Decimal)">
            <summary>
            将数据转换为Decimal  转换失败返回 默认值
            </summary>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToDecimal(System.String,System.Decimal)">
            <summary>
            将数据转换为Decimal  转换失败返回 默认值
            </summary>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToDateTime``1(``0,System.DateTime)">
            <summary>
            将数据转换为DateTime  转换失败返回默认值
            </summary>
            <typeparam name="T">数据类型</typeparam>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToDateTime(System.Object,System.DateTime)">
            <summary>
            将数据转换为DateTime  转换失败返回 默认值
            </summary>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ToDateTime(System.String,System.DateTime)">
            <summary>
            将数据转换为DateTime  转换失败返回 默认值
            </summary>
            <param name="data">数据</param>
            <param name="defValue">默认值</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ConvertToSBC(System.String)">
             <summary>
             转全角的函数(SBC case)
             </summary>
             <param name="input">任意字符串</param>
             <returns>全角字符串</returns>
            <remarks>
            全角空格为12288，半角空格为32
            其他字符半角(33-126)与全角(65281-65374)的对应关系是：均相差65248
            </remarks>
        </member>
        <member name="M:CCWin.SkinClass.Conversion.ConvertToDBC(System.String)">
             <summary> 转半角的函数(DBC case) </summary>
             <param name="input">任意字符串</param>
             <returns>半角字符串</returns>
            <remarks>
            全角空格为12288，半角空格为32
            其他字符半角(33-126)与全角(65281-65374)的对应关系是：均相差65248
            </remarks>
        </member>
        <member name="T:CCWin.SkinClass.DesktopIcon">
            <summary>
            获得桌面图标名称和位置
            </summary>
        </member>
        <member name="M:CCWin.SkinClass.DesktopIcon.ListView_GetItemCount(System.IntPtr)">
            <summary>
            节点个数,通过SendMessage 发送获取
            </summary>
            <param name="AHandle"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.DesktopIcon.ListView_GetItemPosition(System.IntPtr,System.Int32,System.IntPtr)">
            <summary>
            图标位置
            </summary>
            <param name="AHandle"></param>
            <param name="AIndex"></param>
            <param name="APoint"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.DesktopIcon.GetIcoName">
            <summary>
            获取桌面项目的名称
            </summary>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.DesktopIcon.GetIcoPoint(System.String)">
            <summary>
            根据项目名称获得ICO图标位置
            </summary>
            <param name="icoName">项目名称</param>
            <returns></returns>
        </member>
        <member name="T:CCWin.SkinClass.DrawHelper">
            <summary>
            关于 GDI+ 绘图的辅助类
            </summary>
        </member>
        <member name="M:CCWin.SkinClass.DrawHelper.RendererBackground(System.Drawing.Graphics,System.Drawing.Rectangle,System.Drawing.Image,System.Boolean)">
            <summary>
            渲染背景图片,使背景图片不失真
            </summary>
            <param name="g"></param>
            <param name="rect"></param>
            <param name="backgroundImage"></param>
            <param name="method"></param>
        </member>
        <member name="M:CCWin.SkinClass.DrawHelper.RendererBackground(System.Drawing.Graphics,System.Drawing.Rectangle,System.Int32,System.Drawing.Image)">
            <summary>
            渲染背景图片,使背景图片不失真
            </summary>
            <param name="g"></param>
            <param name="rect"></param>
            <param name="cut"></param>
            <param name="backgroundImage"></param>
        </member>
        <member name="M:CCWin.SkinClass.DrawHelper.DrawImage(System.Drawing.Graphics,System.Drawing.Image,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="g"></param>
            <param name="image"></param>
            <param name="x1"></param>
            <param name="y1"></param>
            <param name="width1"></param>
            <param name="height1"></param>
            <param name="x2"></param>
            <param name="y2"></param>
            <param name="width2"></param>
            <param name="height2"></param>
        </member>
        <member name="M:CCWin.SkinClass.DrawHelper.CreateRoundPath(System.Drawing.Rectangle,System.Int32)">
            <summary>
            构建圆角路径
            </summary>
            <param name="rect"></param>
            <param name="cornerRadius"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.DrawHelper.CreateRoundRect(System.Drawing.RectangleF,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            构建圆角路径
            </summary>
            <param name="r"></param>
            <param name="r1"></param>
            <param name="r2"></param>
            <param name="r3"></param>
            <param name="r4"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.GraphicsPathHelper.CreatePath(System.Drawing.Rectangle,System.Int32,CCWin.SkinClass.RoundStyle,System.Boolean)">
            <summary>
            建立带有圆角样式的路径。
            </summary>
            <param name="rect">用来建立路径的矩形。</param>
            <param name="_radius">圆角的大小。</param>
            <param name="style">圆角的样式。</param>
            <param name="correction">是否把矩形长宽减 1,以便画出边框。</param>
            <returns>建立的路径。</returns>
        </member>
        <member name="M:CCWin.SkinClass.ImageDrawRect.DrawRect(System.Drawing.Graphics,System.Drawing.Bitmap,System.Drawing.Rectangle,System.Drawing.Rectangle,System.Int32,System.Int32)">
            <summary>
            绘图对像
            </summary>
            <param name="g">绘图对像</param>
            <param name="img">图片</param>
            <param name="r">绘置的图片大小、坐标</param>
            <param name="lr">绘置的图片边界</param>
            <param name="index">当前状态</param> 
            <param name="Totalindex">状态总数</param>
        </member>
        <member name="M:CCWin.SkinClass.ImageDrawRect.DrawRect(System.Drawing.Graphics,System.Drawing.Bitmap,System.Drawing.Rectangle,System.Int32,System.Int32)">
            <summary>
            绘图对像
            </summary>
            <param name="g"> 绘图对像</param>
            <param name="obj">图片对像</param>
            <param name="r">绘置的图片大小、坐标</param>
            <param name="index">当前状态</param>
            <param name="Totalindex">状态总数</param>
        </member>
        <member name="M:CCWin.SkinClass.ImageObject.GetResBitmap(System.String)">
            <summary>
            得到要绘置的图片对像
            </summary>
            <param name="str">图像在程序集中的地址</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.ImageObject.FindStream(System.String)">
            <summary>
            得到图程序集中的图片对像
            </summary>
            <param name="str">图像在程序集中的地址</param>
            <returns></returns>
        </member>
        <member name="T:CCWin.SkinClass.RoundStyle">
            <summary>
            建立圆角路径的样式。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.RoundStyle.None">
            <summary>
            四个角都不是圆角。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.RoundStyle.TopLeft">
            <summary>
            左上角为圆角。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.RoundStyle.TopRight">
            <summary>
            右上角为圆角。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.RoundStyle.BottomLeft">
            <summary>
            左下角为圆角。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.RoundStyle.BottomRight">
            <summary>
            右下角为圆角。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.RoundStyle.Left">
            <summary>
            左边两个角为圆角。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.RoundStyle.Right">
            <summary>
            右边两个角为圆角。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.RoundStyle.Top">
            <summary>
            上边两个角为圆角。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.RoundStyle.Bottom">
            <summary>
            下边两个角为圆角。
            </summary>
        </member>
        <member name="F:CCWin.SkinClass.RoundStyle.All">
            <summary>
            四个角都为圆角。
            </summary>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.SetFormOpacity(System.Windows.Forms.Form,System.Double)">
            <summary>
            通过API函数设置Form透明度
            </summary>
            <param name="form">窗体</param>
            <param name="opacity">透明度0.00-1</param>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.GaryImg(System.Drawing.Bitmap)">
            <summary>
            将图像转换成灰色介
            </summary>
            <returns>灰色图像</returns>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.GetImageAverageColor(System.Drawing.Bitmap)">
            <summary>
            获取图片主色调
            </summary>
            <param name="back">图片</param>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.ColorSlantsDarkOrBright(System.Drawing.Color)">
            <summary>
            判断颜色偏向于暗色或亮色(true为偏向于暗色，false位偏向于亮色。)
            </summary>
            <param name="c">要判断的颜色</param>
            <returns>true为偏向于暗色，false位偏向于亮色。</returns>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.CreateRegion(System.Windows.Forms.Control,System.Int32)">
            <summary>
            绘制组件圆角
            </summary>
            <param name="frm">要绘制的组件</param>
            <param name="RgnRadius">圆角大小</param>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.CreateRegion(System.Windows.Forms.Control,System.Drawing.Rectangle,System.Int32,CCWin.SkinClass.RoundStyle)">
            <summary>
            样式绘制圆角
            </summary>
            <param name="control">控件</param>
            <param name="bounds">范围</param>
            <param name="radius">圆角大小</param>
            <param name="roundStyle">圆角样式</param>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.CreateRegion(System.Windows.Forms.Control,System.Drawing.Rectangle)">
            <summary>
            绘制四个角弧度为8
            </summary>
            <param name="control">控件</param>
            <param name="bounds">范围</param>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.CreateRegion(System.IntPtr,System.Int32,CCWin.SkinClass.RoundStyle,System.Boolean)">
            <summary>
            样式绘制圆角
            </summary>
            <param name="hWnd">控件句柄</param>
            <param name="radius">圆角</param>
            <param name="roundStyle">圆角样式</param>
            <param name="redraw">是否重画</param>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.BothAlpha(System.Drawing.Bitmap,System.Boolean,System.Boolean)">
            <summary>
            设置图形边缘半透明
            </summary>
            <param name="p_Bitmap">图形</param>
            <param name="p_CentralTransparent">true中心透明 false边缘透明</param>
            <param name="p_Crossdirection">true横 false纵</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.ImageLightEffect(System.String,System.Drawing.Font,System.Drawing.Color,System.Drawing.Color,System.Int32)">
            <summary>
            绘制发光字体
            </summary>
            <param name="Str">字体</param>
            <param name="F">字体样式</param>
            <param name="ColorFore">字体颜色</param>
            <param name="ColorBack">光圈颜色</param>
            <param name="BlurConsideration">光圈大小</param>
            <returns>Image格式图</returns>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.ImageLightEffect(System.String,System.Drawing.Font,System.Drawing.Color,System.Drawing.Color,System.Int32,System.Drawing.Rectangle,System.Boolean)">
            <summary>
            范围绘制发光字体
            </summary>
            <param name="Str">字体</param>
            <param name="F">字体样式</param>
            <param name="ColorFore">字体颜色</param>
            <param name="ColorBack">光圈颜色</param>
            <param name="BlurConsideration">光圈大小</param>
            <param name="rc">文字范围</param>
            <param name="auto">是否启用范围绘制发光字体</param>
            <returns>Image格式图</returns>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.CursorClick(System.Int32,System.Int32)">
            <summary>
            执行一次鼠标点击
            </summary>
            <param name="x">X</param>
            <param name="y">Y</param>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.ResizeBitmap(System.Drawing.Bitmap,System.Int32,System.Int32)">
            <summary>
            图片缩放
            </summary>
            <param name="b">源图片Bitmap</param>
            <param name="dstWidth">目标宽度</param>
            <param name="dstHeight">目标高度</param>
            <returns>处理完成的图片 Bitmap</returns>
        </member>
        <member name="M:CCWin.SkinClass.SkinTools.CreateControlRegion(System.Windows.Forms.Control,System.Drawing.Bitmap,System.Int32)">
            <summary> 
            创建支持位图区域的控件（目前有button和form）
            </summary> 
            <param name="control">控件</param> 
            <param name="bitmap">位图</param>
            <param name="Alpha">小于此透明值的去除</param> 
        </member>
        <member name="M:CCWin.SkinControl.RichTxtControl.CtrlMouseDown(CCWin.SkinControl.SkinChatRichTextBox,System.Drawing.Point,System.Windows.Forms.MouseEventArgs)">
            <summary>
            子控件点击事件
            </summary>
            <param name="richTextBox">所在的richTextBox</param>
            <param name="newP">光标在控件上的位置</param>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.RichTxtControl.CtrlMouseMove(CCWin.SkinControl.SkinChatRichTextBox,System.Drawing.Point,System.Windows.Forms.MouseEventArgs)">
            <summary>
            子控件移动事件
            </summary>
            <param name="richTextBox">所在的richTextBox</param>
            <param name="newP">光标在控件上的位置</param>
            <param name="e"></param>
        </member>
        <member name="F:CCWin.SkinControl.RichTxtControl.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.RichTxtControl.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.RichTxtControl.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.SkinChatRichTextBox">
            <summary>
            支持图片和动画的RichTextBox。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.InsertLink(System.String)">
            <summary>
            插入一个指定链接文本到richtextbox。
            </summary>
            <param name="text">要插入的文本</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.InsertLink(System.String,System.Int32)">
            <summary>
            在一个指定位置插入链接文本到richtextbox。
            </summary>
            <param name="text">要插入的文本</param>
            <param name="position">插入位置</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.InsertLink(System.String,System.String)">
            <summary>
            在当前指定位置插入超链接字符串和超链接到richtextbox。
            </summary>
            <param name="text">要插入的文本</param>
            <param name="hyperlink">要插入的超链接字符串</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.InsertLink(System.String,System.String,System.Int32)">
            <summary>
            在一个指定位置插入超链接字符串和超链接到richtextbox。
            </summary>
            <param name="text">要插入的文本</param>
            <param name="hyperlink">要插入的超链接字符串</param>
            <param name="position">插入位置</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.SetSelectionLink(System.Boolean)">
            <summary>
            设置当前选择的链接样式
            </summary>
            <param name="link">true: 设置链接风格, false: 清除链接风格</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.GetSelectionLink">
            <summary>
            获取当前选择的链接样式
            </summary>
            <returns>0: 没有设置链接风格, 1: 链接样式设置, -1: 混合</returns>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.GetControl(System.Drawing.Point,System.Boolean)">
            <summary>
            根据Point位置获取文本框内控件
            </summary>
            <param name="pt"></param>
            <param name="selectTarget"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.InsertControl(System.Windows.Forms.Control)">
            <summary>
            插入控件到末尾
            </summary>
            <param name="_oCtrl">控件</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.InsertControl(System.Windows.Forms.Control,System.Int32,System.UInt32)">
            <summary>
            插入控件到指定位置
            </summary>
            <param name="_oCtrl">控件</param>
            <param name="_nPostion">位置</param>
            <param name="dwUser">Tag</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.InsertDefaultEmotion(System.UInt32)">
            <summary>
            插入表情
            </summary>
            <param name="emotionID">表情ID</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.InsertDefaultEmotion(System.UInt32,System.Int32)">
            <summary>
            在position位置处，插入系统内置表情。
            </summary>      
            <param name="position">插入的位置</param>
            <param name="emotionID">表情图片在内置列表中的index</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.InsertImage(System.Drawing.Image)">
            <summary>
            插入图片
            </summary>
            <param name="image">图片</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.InsertImage(System.Drawing.Image,System.Int32)">
            <summary>
            在position位置处，插入图片。
            </summary>   
            <param name="image">要插入的图片</param>
            <param name="position">插入的位置</param>       
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.GetContent">
            <summary>
            获取Box中的所有内容。
            </summary>        
            <param name="containsForeignObject">内容中是否包含不是由IImagePathGetter管理的图片对象</param>
            <returns>key为位置，val为图片的ID</returns>
        </member>
        <member name="M:CCWin.SkinControl.SkinChatRichTextBox.AppendRichText(System.String,System.Drawing.Font,System.Drawing.Color)">
            <summary>
            在现有内容后面追加富文本。
            </summary>      
        </member>
        <member name="E:CCWin.SkinControl.SkinChatRichTextBox.FileOrFolderDragDrop">
            <summary>
            当文件（夹）拖放到控件内时，触发此事件。参数：文件路径的集合。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinChatRichTextBox.ContextMenuMode">
            <summary>
            快捷菜单的模式。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinChatRichTextBox.PopoutImageWhenDoubleClick">
            <summary>
            双击图片时，是否弹出图片。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinChatRichTextBox.IsTrank">
            <summary>
            背景是否透明
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinChatRichTextBox.SelectControl">
            <summary>
            选中的控件
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinChatRichTextBox.SelectControlPoint">
            <summary>
            选中的控件的索引位置点Point
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinChatRichTextBox.SelectControlIndex">
            <summary>
            选中的控件的索引位置
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinChatRichTextBox.ListControl">
            <summary>
            获取文本框内所有控件
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatBoxContent.Text">
            <summary>
            纯文本信息
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatBoxContent.ForeignImageDictionary">
            <summary>
            非内置的表情图片。key - 在ChatBox中的位置。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatBoxContent.EmotionDictionary">
            <summary>
            内置的表情图片。key - 在ChatBox中的位置 ，value - 表情图片在内置列表中的index。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatBoxContent.PicturePositions">
            <summary>
            所有图片的位置。从小到大排列。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ImageForm.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ImageForm.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinControl.ImageForm.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinDateTimePicker.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinDateTimePicker.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinDateTimePicker.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinFlowLayoutPanel.UpdateRadius">
            <summary>
            更新圆角
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinFlowLayoutPanel.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinFlowLayoutPanel.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinFlowLayoutPanel.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinFlowLayoutPanel.ControlState">
            <summary>
            控件状态
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinFlowLayoutPanel.Palace">
            <summary>
            是否开启九宫绘图
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinFlowLayoutPanel.BackRectangle">
            <summary>
            九宫绘画区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinFlowLayoutPanel.MouseBack">
            <summary>
            悬浮时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinFlowLayoutPanel.DownBack">
            <summary>
            点击时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinFlowLayoutPanel.NormlBack">
            <summary>
            初始时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinFlowLayoutPanel.Radius">
            <summary>
            圆角大小
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.SkinHotKey">
            <summary>
            提供自定义全局热键支持
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinHotKey.#ctor(CCWin.SkinControl.KeyModifiers,System.Windows.Forms.Keys)">
            <summary>
            初始化热键
            </summary>
            <param name="keyModifier">辅助键</param>
            <param name="key">按键</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinHotKey.#ctor">
            <summary>
            初始化热键
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinHotKey.#ctor(System.ComponentModel.IContainer)">
            <summary>
            初始化热键
            </summary>             
        </member>
        <member name="M:CCWin.SkinControl.SkinHotKey.RegisterHotKey">
            <summary>
            注册热键
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinHotKey.UnregisterHotKey">
            <summary>
            卸载热键
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinHotKey.Activate">
            <summary>
            启用
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinHotKey.Id">
            <summary>
            热键ID
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinHotKey.KeyModifier">
            <summary>
            辅助按键
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinHotKey.Key">
            <summary>
            按键必须，必须填写按键，请勿添加修饰键，修饰键在辅助键 KeyModifier 属性那边设置
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinHotKey.IsRegistered">
            <summary>
            是否已成功注册
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinHotKey.Enabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinHotKey.Handle">
            <summary>
            热键所注册的句柄
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinHotKey.Tag">
            <summary>
            与对象关联的用户定义数据
            </summary>
        </member>
        <member name="E:CCWin.SkinControl.SkinHotKey.HotKeyTrigger">
            <summary>
            热键被按下的事件
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinHotKey.HotKeyWindow.CreateHandle">
            <summary>
            创建句柄
            </summary>
            <returns></returns>
        </member>
        <member name="T:CCWin.SkinControl.HotKeyEventArgs">
            <summary>
            热键事件数据类
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.HotKeyEventArgs.#ctor(CCWin.SkinControl.KeyModifiers,System.Windows.Forms.Keys)">
            <summary>
            初始化数据
            </summary>
            <param name="id"></param>
            <param name="keyModifier"></param>
            <param name="key"></param>
        </member>
        <member name="P:CCWin.SkinControl.HotKeyEventArgs.KeyModifier">
            <summary>
            辅助按键
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.HotKeyEventArgs.Key">
            <summary>
            按键
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.KeyModifiers">
            <summary>
            定义了辅助键的名称（将数字转变为字符以便于记忆，也可去除此枚举而直接使用数值）
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.GeneralUtil.IsNull(System.Object)">
            <summary>
            判断对象是否为空
            </summary>
            <param name="obj">对象</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.GeneralUtil.IsNullOrEmpty(System.String)">
            <summary>
            判断字符串是否为空
            </summary>
            <param name="str">字符串</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.GeneralUtil.ParseInt(System.String)">
            <summary>
            将 String 类型转换为 int 型,转换失败返回 int.MinValue
            </summary>
            <param name="strValue">待转换 String</param>
            <returns>返回 int</returns>
        </member>
        <member name="M:CCWin.SkinControl.GeneralUtil.ParseInt(System.Object)">
            <summary>
            将 Object 类型转换为 int 型,转换失败时返回 int.MinValue
            </summary>
            <param name="objValue">待转换 Object</param>      
            <returns>返回 int</returns>
        </member>
        <member name="M:CCWin.SkinControl.GeneralUtil.TryParseByte(System.String,System.Byte)">
            <summary>
            Method to perform a parse of a string into a byte number
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.GeneralUtil.TryParseUshort(System.String,System.UInt16)">
            <summary>
            Method to perform a parse of a string into a ushort number
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.GeneralUtil.TryParseEnum(System.Type,System.String,System.Object)">
            <summary>
            Method to perform a parse of the string into an enum
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.GeneralUtil.IsStringEqual(System.String,System.String)">
            <summary>
            Method to determine if the tag name is of the correct type
            A string comparision is made whilst ignoring case
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinLine.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinLine.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinLine.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinLine.LineHeight">
            <summary>
            线高度
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinLine.LineColor">
            <summary>
            前景色
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.MdiClientController">
            <summary>
            Listens for messages sent to a <see cref="T:System.Windows.Forms.MdiClient"/>
            class and controls its properties.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.MdiClientController.#ctor">
            <summary>
            Initializes a new instance of the <see cref="!:Slusser.Components.MdiClientController"/> class.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.MdiClientController.#ctor(System.Windows.Forms.Form)">
            <summary>
            Initializes a new instance of the <see cref="!:Slusser.Components.MdiClientController"/> class
            for the given MDI form.
            </summary>
            <param name="parentForm">The MDI form.</param>
        </member>
        <member name="M:CCWin.SkinControl.MdiClientController.Dispose">
            <summary>
            Releases all resources used by the
            <see cref="T:System.ComponentModel.Component"/>.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.MdiClientController.RenewMdiClient">
            <summary>
            Reestablishes a connection to the <see cref="T:System.Windows.Forms.MdiClient"/>
            control if the <see cref="!:Slusser.Components.MdiClientController.ParentForm"/>
            hasn't changed but its <see cref="P:System.Windows.Forms.Form.IsMdiContainer"/>
            property has.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.MdiClientController.Dispose(System.Boolean)">
            <summary>Releases the unmanaged resources used by the
            <see cref="T:System.ComponentModel.Component"/> and optionally releases the
            managed resources.</summary>
            <param name="disposing"><c>true</c> to release both managed and unmanaged
            resources; <c>false</c> to release only unmanaged resources.</param>
        </member>
        <member name="M:CCWin.SkinControl.MdiClientController.WndProc(System.Windows.Forms.Message@)">
            <summary>
            Invokes the default window procedure associated with this window.
            </summary>
            <param name="m">A <see cref="T:System.Windows.Forms.Message"/> that is associated with the current Windows message. </param>
        </member>
        <member name="M:CCWin.SkinControl.MdiClientController.OnPaint(System.Windows.Forms.PaintEventArgs)">
            <summary>
            Raises the <see cref="!:Slusser.Components.MdiClientController.Paint"/> event.
            </summary>
            <param name="e">A <see cref="T:System.Windows.Forms.PaintEventArgs"/> that
            contains the event data.</param>
        </member>
        <member name="M:CCWin.SkinControl.MdiClientController.OnHandleAssigned(System.EventArgs)">
            <summary>
            Raises the <see cref="!:Slusser.Components.MdiClientController.HandleAssigned"/> event.
            </summary>
            <param name="e">A <see cref="T:System.EventArgs"/> that contains the event
            data.</param>
        </member>
        <member name="E:CCWin.SkinControl.MdiClientController.Paint">
            <summary>
            Occurs when the control is redrawn.
            </summary>
        </member>
        <member name="E:CCWin.SkinControl.MdiClientController.Disposed">
            <summary>
            Occurs when the control is disposed.
            </summary>
        </member>
        <member name="E:CCWin.SkinControl.MdiClientController.HandleAssigned">
            <summary>
            Occurs when the <see cref="T:System.Windows.Forms.NativeWindow"/> handle
            is assigned.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.MdiClientController.Site">
            <summary>
            Gets or sets the <see cref="T:System.ComponentModel.ISite"/> of
            the <see cref="T:System.ComponentModel.Component"/>.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.MdiClientController.ParentForm">
            <summary>
            Gets or sets the form that the <see cref="T:System.Windows.Forms.MdiClient"/>
            control is assigned to.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.MdiClientController.MdiClient">
            <summary>
            Gets the <see cref="T:System.Windows.Forms.MdiClient"/> being controlled.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.MdiClientController.BackColor">
            <summary>
            Gets or sets the background color for the control.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.MdiClientController.BorderStyle">
            <summary>
            Indicates the border style for the control.
            </summary>
            <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The value
            assigned to the property is not a member of
            the <see cref="T:System.Windows.Forms.BorderStyle"/> enumeration.</exception>
        </member>
        <member name="P:CCWin.SkinControl.MdiClientController.AutoScroll">
            <summary>
            Gets or sets a value indicating whether the container will allow the user to
            scroll to any controls placed outside of its visible boundaries.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.MdiClientController.Image">
            <summary>
            Gets or sets the image that the
            <see cref="!:Slusser.Components.MdiClientController"/> displays.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.MdiClientController.ImageAlign">
            <summary>
            Gets or sets the alignment of the background image.
            </summary>
            <exception cref="T:System.ComponentModel.InvalidEnumArgumentException">The
            value assigned to the property is not a member of
            the <see cref="T:System.Drawing.ContentAlignment"/> enumeration.</exception>
        </member>
        <member name="P:CCWin.SkinControl.MdiClientController.StretchImage">
            <summary>
            Gets or sets a value indicating whether the
            <see cref="!:Slusser.Components.MdiClientController.Image"/> should
            be stretched to fill the entire client area.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.MdiClientController.Handle">
            <summary>
            Gets the handle for this window.
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.SkinMonthCalendar">
            <summary>
            SkinMonthCalendar控件
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinMonthCalendar.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinMonthCalendar.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinMonthCalendar.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinDropDown.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinDropDown.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinDropDown.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ChatListClickEventArgs.#ctor(CCWin.SkinControl.ChatListSubItem)">
            <summary>
            悬浮事件
            </summary>
            <param name="selectsubitem">选中的好友</param>
        </member>
        <member name="P:CCWin.SkinControl.ChatListClickEventArgs.SelectSubItem">
            <summary>
            选中的好友
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinCode.NewCode">
            <summary>
            创建新的Code验证码，并返回其验证码
            </summary>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.SkinCode.OnCreateControl">
            <summary>
            控件创建时
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinCode.OnClick(System.EventArgs)">
            <summary>
            点击换验证码
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinCode.OnForeColorChanged(System.EventArgs)">
            <summary>
            字体颜色改变时
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinCode.OnBackColorChanged(System.EventArgs)">
            <summary>
            背景色改变时
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinCode.UpdateImg">
            <summary>
            重绘验证码图片颜色信息
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinCode.OnPaint(System.Windows.Forms.PaintEventArgs)">
            <summary>
            重绘时
            </summary>
            <param name="pe"></param>
        </member>
        <member name="F:CCWin.SkinControl.SkinCode.rand">
            <summary> 
            生成随机的字母   
            </summary>    
            <param name="VcodeNum">生成字母的个数</param>                                                                
            <returns>string</returns>
        </member>
        <member name="F:CCWin.SkinControl.SkinCode.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinCode.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinCode.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCode.CodeImg">
            <summary>
            验证码的图像
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCode.CodeStr">
            <summary>
            验证码的值
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCode.ClickNewCode">
            <summary>
            是否可以点击刷新验证码
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCode.Color_BackGround">
            <summary>
            背景颜色集
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCode.VcArray">
            <summary>
            验证码字符集
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCode.CodeCount">
            <summary>
            验证码字数
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinListView.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinListView.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinListView.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinRollingBar.currentAngle">
            <summary>
            以角度为单位的当前的角度位置
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRollingBar.RefleshFrequency">
            <summary>
            刷新频率
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRollingBar.Style">
            <summary>
            当前样式
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRollingBar.XTheme">
            <summary>
            当前主题
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRollingBar.Radius1">
            <summary>
            外圈大小
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRollingBar.Radius2">
            <summary>
            内圈大小
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRollingBar.SpokeNum">
            <summary>
            获取或设置用于在动画圈的残影数量
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRollingBar.PenWidth">
            <summary>
            画笔宽度
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRollingBar.SkinBackColor">
            <summary>
            背景色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRollingBar.BaseColor">
            <summary>
            主色调
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRollingBar.DiamondColor">
            <summary>
            菱形块颜色
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.GraphicsMode.#ctor(System.Drawing.Graphics,System.Drawing.Drawing2D.SmoothingMode)">
            <summary>
            Initialize a new instance of the class.
            </summary>
            <param name="g">Graphics instance.</param>
            <param name="mode">Desired Smoothing mode.</param>
        </member>
        <member name="M:CCWin.SkinControl.GraphicsMode.Dispose">
            <summary>
            Revert the SmoothingMode to original setting.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.cStoreDc.CreateDCA(System.String,System.String,System.String,System.Int32)">
            <summary>
            函数功能：该函数通过使用指定的名字为一个设备创建设备上下文环境。 
            </summary>
            <param name="lpszDriver">指向一个以Null结尾的字符串的指针，该字符串为显示驱动指定DISPLAY或者指定一个打印驱动程序名，通常为WINSPOOL。 </param>
            <param name="lpszDevice">指向一个以null结尾的字符串的指针，该字符串指定了正在使用的特定输出设备的名字，它不是打印机模式名。LpszDevice参数必须被使用。</param>
            <param name="lpszOutput">该参数在32位应用中被忽略；并置为Null，它主要是为了提供与16位应用程序兼容，更多的信息参见下面的注释部分。 </param>
            <param name="lpInitData">指向包含设备驱动程序的设备指定初始化数据的DEVMODE结构的指针，DocumentProperties函数检索指定设备获取已填充的结构，如果设备驱动程序使用用户指定的缺省初始化值。则lplnitData参数必须为Null。 </param>
            <returns>返回值：成功，返回值是特定设备的设备上下文环境的句柄；失败，返回值为Null。</returns>
        </member>
        <member name="M:CCWin.SkinControl.cStoreDc.CreateDCW(System.String,System.String,System.String,System.Int32)">
            <summary>
            函数功能:该函数通过使用指定的名字为一个设备创建设备上下文环境
            </summary>
            <param name="lpszDriver"></param>
            <param name="lpszDevice"></param>
            <param name="lpszOutput"></param>
            <param name="lpInitData"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.cStoreDc.CreateDC(System.String,System.String,System.String,System.Int32)">
            <summary>
            函数功能:该函数通过使用指定的名字为一个设备创建设备上下文环境
            </summary>
            <param name="lpszDriver"></param>
            <param name="lpszDevice"></param>
            <param name="lpszOutput"></param>
            <param name="lpInitData"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.cStoreDc.CreateCompatibleDC(System.IntPtr)">
            <summary>
            该函数创建一个与指定设备兼容的内存设备上下文环境（DC）。通过GetDc()获取的HDC直接与相关设备沟通，而本函数创建的DC，则是与内存中的一个表面相关联。
            </summary>
            <param name="hdc"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.cStoreDc.CreateCompatibleBitmap(System.IntPtr,System.Int32,System.Int32)">
            <summary>
            函数功能:该函数创建与指定的设备环境相关的设备兼容的位图
            </summary>
            <param name="hdc"></param>
            <param name="nWidth"></param>
            <param name="nHeight"></param>
            <returns></returns>
        </member>
        <member name="T:CCWin.SkinControl.ScrollBarDrawImage">
            <summary>
            滚动条绘制图片-统一设置
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarDrawImage.ScrollHorzShaft">
            <summary>
            横向滚动条轴
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarDrawImage.ScrollHorzArrow">
            <summary>
            横向滚动条箭头
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarDrawImage.ScrollHorzThumb">
            <summary>
            横向滚动条滑块
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarDrawImage.ScrollVertShaft">
            <summary>
            竖向滚动条轴
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarDrawImage.ScrollVertArrow">
            <summary>
            竖向滚动条箭头
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarDrawImage.ScrollVertThumb">
            <summary>
            竖向滚动条滑块
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarDrawImage.Fader">
            <summary>
            右下角推块
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarHelper.GWL_STYLE">
            <summary>
            获得风格
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarHelper.GWL_EXSTYLE">
            <summary>
            获得扩展风格
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarHelper.WM_PAINT">
            <summary>
            用于重画窗口的用户区
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarHelper.WM_NCPAINT">
            <summary>
            用于重画窗口的非用户区，如标题，边框和滚动杆
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarHelper.WM_MOUSEMOVE">
            <summary>
            消息在鼠标移动时被发送至已获焦点的窗口
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarHelper.WM_MOUSELEAVE">
            <summary>
            WM_MOUSELEAVE 是鼠标离开窗口时发出的消息，但是这个消息与普通的鼠标消息不同，要收到WM_MOUSELEAVE消息必须先调用TrackMouseEvent，
            并且每调用一次TrackMouseEvent 窗口只能收到一次WM_MOUSELEAVE，也就说如果要获得WM_MOUSELEAVE消息的话，当鼠标重新进入窗口时必须调
            用一次TrackMouseEvent。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ScrollBarHelper.OffsetRect(CCWin.SkinControl.ScrollBarHelper.RECT@,System.Int32,System.Int32)">
            <summary>
            OffsetRect函数将指定的矩形移动到指定的位置 
            </summary>
            <param name="lpRect">[输入输出]指向一个RECT结构，其中包含了被移动矩形的逻辑坐标</param>
            <param name="x">[输入]指定的矩形左右移动的量。当向左移动的时候，这个参数必须是一个负值</param>
            <param name="y">[输出]指定的矩形上下移动的量。当想上移动的时候，这个参数应该是一个负值。</param>
            <returns>如果函数成功，返回非0，否则返回0。 </returns>
        </member>
        <member name="M:CCWin.SkinControl.ScrollBarHelper.GetWindowLong(System.IntPtr,System.Int32)">
            <summary>
            GetWindowLong是一个函数。该函数获得有关指定窗口的信息，函数也获得在额外窗口内存中指定偏移位地址的32位度整型值。
            </summary>
            <param name="hwnd"></param>
            <param name="nIndex"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.ScrollBarHelper.ShowWindow(System.IntPtr,System.Int32)">
            <summary>
            函数功能：该函数设置指定窗口的显示状态
            </summary>
            <param name="hWnd">指窗口句柄</param>
            <param name="nCmdShow">指定窗口如何显示</param>
            <returns>如果窗口当前可见，则返回值为非零。如果窗口当前被隐藏，则返回值为零</returns>
        </member>
        <member name="M:CCWin.SkinControl.ScrollBarHelper.GetScrollBarInfo(System.IntPtr,System.UInt32,CCWin.SkinControl.ScrollBarHelper.SCROLLBARINFO@)">
            <summary>
             检索有关指定滚动条信息。
            </summary>
            <param name="hWnd"></param>
            <param name="idObject"></param>
            <param name="psbi"></param>
            <returns></returns>
        </member>
        <member name="F:CCWin.SkinControl.ScrollBarHelper._hSizerMaskWnd">
            <summary>
            当前窗口句柄
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ScrollBarHelper.checkBarState">
            <summary>
            滚动条状态
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ScrollBarHelper.drawScrollBar">
            <summary>
            画滚动条
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ScrollBarHelper.hasHorizontal">
            <summary>
            是否存在水平滚动条
            </summary>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.ScrollBarHelper.hasSizer">
            <summary>
            垂直/水平滚动条都存在
            </summary>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.ScrollBarHelper.hasVertical">
            <summary>
            是否存在垂直滚动条
            </summary>
            <returns></returns>
        </member>
        <member name="T:CCWin.SkinControl.ScrollBarHelper.RECT">
            <summary>
            来存储一个矩形框的左上角坐标、宽度和高度
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.ScrollBarHelper.SCROLLBARINFO">
            <summary>
            一个SCROLLBARINFO结构，以获得的信息。在调用GetScrollBarInfo，设置cbSize成员为sizeof（SCROLLBARINFO）。
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.Animation">
            <summary>
            动画控制器
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.Controller">
            <summary>
            DoubleBitmap displays animation
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.DoubleBitmapControl.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.DoubleBitmapControl.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinControl.DoubleBitmapControl.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.PointFConverter">
            <summary>
            PointFConverter
            Thanks for Jay Riggs
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.PointFConverter.#ctor">
            <summary>
            Creates a new instance of PointFConverter
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.PointFConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Boolean, true if the source type is a string
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.PointFConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the specified string into a PointF
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.PointFConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>
            Converts the PointF into a string
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.SkinAnimator">
            <summary>
            动画管理器
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimator.CheckRequests">
            <summary>
            检查控制结果状态
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimator.Show(System.Windows.Forms.Control,System.Boolean,CCWin.SkinControl.Animation)">
            <summary>
            显示控制。作为结果的控制将显示动画。
            </summary>
            <param name="control">目标控制</param>
            <param name="parallel">允许动画等动画同时</param>
            <param name="animation">个人动画</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimator.ShowSync(System.Windows.Forms.Control,System.Boolean,CCWin.SkinControl.Animation)">
            <summary>
            显示控制和等待在动画将完成。作为结果的控制将显示动画。
            </summary>
            <param name="control">目标控制</param>
            <param name="parallel">允许动画等动画同时</param>
            <param name="animation">个人动画</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimator.Hide(System.Windows.Forms.Control,System.Boolean,CCWin.SkinControl.Animation)">
            <summary>
            隐藏的控制。作为结果的控制将被隐藏的动画。
            </summary>
            <param name="control">目标控制</param>
            <param name="parallel">允许动画等动画同时</param>
            <param name="animation">个人动画</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimator.HideSync(System.Windows.Forms.Control,System.Boolean,CCWin.SkinControl.Animation)">
            <summary>
            隐藏的控制和等待在动画将完成。作为结果的控制将被隐藏的动画。
            </summary>
            <param name="control">目标控制</param>
            <param name="parallel">允许动画等动画同时</param>
            <param name="animation">个人动画</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimator.BeginUpdateSync(System.Windows.Forms.Control,System.Boolean,CCWin.SkinControl.Animation,System.Drawing.Rectangle)">
            <summary>
            这使得控制快照更新前。它需要更新调用结束。
            </summary>
            <param name="control">目标控制</param>
            <param name="parallel">允许动画等动画同时</param>
            <param name="animation">个人动画</param>
            <param name="clipRectangle">动画剪辑矩形</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimator.EndUpdate(System.Windows.Forms.Control)">
            <summary>
            动画视图更新控制。它需要调用开始更新之前。
            </summary>
            <param name="control">目标控制</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimator.EndUpdateSync(System.Windows.Forms.Control)">
            <summary>
            更新控制视图与动画等动画将完成时。它需要调用开始更新之前
            </summary>
            <param name="control">目标控制</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimator.WaitAllAnimations">
            <summary>
            在等待所有的动画将完成。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimator.WaitAnimation(System.Windows.Forms.Control)">
            <summary>
            在等待的控制动画将完成。
            </summary>
            <param name="animatedControl"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimator.AddToQueue(System.Windows.Forms.Control,CCWin.SkinControl.AnimateMode,System.Boolean,CCWin.SkinControl.Animation,System.Drawing.Rectangle)">
            <summary>
            添加到队列控制动画。
            </summary>
            <param name="control">目标控制</param>
            <param name="mode">动画模式</param>
            <param name="parallel">允许动画等动画同时</param>
            <param name="animation">个人动画</param> 
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimator.ClearQueue">
            <summary>
            清除队列。
            </summary>
        </member>
        <member name="E:CCWin.SkinControl.SkinAnimator.AnimationCompleted">
            <summary>
            发生在控制动画完成后
            </summary>
        </member>
        <member name="E:CCWin.SkinControl.SkinAnimator.AllAnimationsCompleted">
            <summary>
            当所有的动画完成时
            </summary>
        </member>
        <member name="E:CCWin.SkinControl.SkinAnimator.TransfromNeeded">
            <summary>
            当需要变换矩阵时
            </summary>
        </member>
        <member name="E:CCWin.SkinControl.SkinAnimator.NonLinearTransfromNeeded">
            <summary>
            当需要非线性变换时
            </summary>
        </member>
        <member name="E:CCWin.SkinControl.SkinAnimator.MouseDown">
            <summary>
            当用户点击动画控制时
            </summary>
        </member>
        <member name="E:CCWin.SkinControl.SkinAnimator.FramePainted">
            <summary>
            发生帧动画绘画时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinAnimator.MaxAnimationTime">
            <summary>
            动画的最大时间（毫秒）
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinAnimator.DefaultAnimation">
            <summary>
            默认的动画
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinAnimator.Cursor">
            <summary>
            动画控制光标
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinAnimator.IsCompleted">
            <summary>
            是否所有动画完成
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinAnimator.Interval">
            <summary>
            帧之间时间间隔（MS）
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinAnimator.AnimationType">
            <summary>
            内置的动画类型
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinAnimator.TimeStep">
            <summary>
            时间步长
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.TransfromHelper">
            <summary>
            Implements image transformations
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.GetMouseSubItemBool">
            <summary>
            判断是否悬浮在好友上，是则返回好友，不是则返回null
            </summary>
            <returns>ChatListSubItem好友对象</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.DrawItem(System.Drawing.Graphics,CCWin.SkinControl.ChatListItem,System.Drawing.Rectangle,System.Drawing.SolidBrush)">
            <summary>
            绘制列表项
            </summary>
            <param name="g">绘图表面</param>
            <param name="item">要绘制的列表项</param>
            <param name="rectItem">该列表项的区域</param>
            <param name="sb">画刷</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.DrawSubItem(System.Drawing.Graphics,CCWin.SkinControl.ChatListSubItem,System.Drawing.Rectangle@,System.Drawing.SolidBrush,System.Boolean)">
            <summary>
            绘制列表子项
            </summary>
            <param name="g">绘图表面</param>
            <param name="subItem">要绘制的子项</param>
            <param name="rectSubItem">该子项的区域</param>
            <param name="sb">画刷</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.DrawHeadImage(System.Drawing.Graphics,CCWin.SkinControl.ChatListSubItem,System.Drawing.Rectangle)">
            <summary>
            绘制列表子项的头像
            </summary>
            <param name="g">绘图表面</param>
            <param name="subItem">要绘制头像的子项</param>
            <param name="rectSubItem">该子项的区域</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.DrawLargeSubItem(System.Drawing.Graphics,CCWin.SkinControl.ChatListSubItem,System.Drawing.Rectangle)">
            <summary>
            绘制大图标模式的个人信息
            </summary>
            <param name="g">绘图表面</param>
            <param name="subItem">要绘制信息的子项</param>
            <param name="rectSubItem">该子项的区域</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.DrawSmallSubItem(System.Drawing.Graphics,CCWin.SkinControl.ChatListSubItem,System.Drawing.Rectangle)">
            <summary>
            绘制小图标模式的个人信息
            </summary>
            <param name="g">绘图表面</param>
            <param name="subItem">要绘制信息的子项</param>
            <param name="rectSubItem">该子项的区域</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.GetSubItemsById(System.UInt32)">
            <summary>
            根据id返回一组列表子项
            </summary>
            <param name="userId">要返回的id</param>
            <returns>列表子项的数组</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.GetSubItemsByNicName(System.String)">
            <summary>
            根据昵称返回一组列表子项
            </summary>
            <param name="nicName">要返回的昵称</param>
            <returns>列表子项的数组</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.GetSubItemsByDisplayName(System.String)">
            <summary>
            根据备注名称返回一组列表子项
            </summary>
            <param name="displayName">要返回的备注名称</param>
            <returns>列表子项的数组</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.GetSubItemsByIp(System.String)">
            <summary>
            根据IP返回一组列表子项
            </summary>
            <param name="Ip">要返回的Ip</param>
            <returns>列表子项的数组</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.GetSubItemsByText(System.String)">
            <summary>
            根据文本搜索
            </summary>
            <param name="nicName">要返回的昵称</param>
            <returns>列表子项的数组</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.Regain">
            <summary>
            恢复到上次打开的用户组
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ChatListBox.scrollSpeed">
            <summary>
            平滑滚动速度
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="F:CCWin.SkinControl.ChatListBox.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ChatListBox.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.RollSize">
            <summary>
            滚轮每格滚动的像素值
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.SmoothScroll">
            <summary>
            是否平滑滚动
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.SubItemMenu">
            <summary>
            当用户右击分组时显示的快捷菜单。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.ListSubItemMenu">
            <summary>
            当用户右击好友时显示的快捷菜单。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.IconSizeMode">
            <summary>
            与列表关联的图标模式
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.Items">
            <summary>
            获取列表中所有列表项的集合
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.SelectSubItem">
            <summary>
            当前选中的子项
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.SelectItem">
            <summary>
            当前选中的组
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.ScrollBackColor">
            <summary>
            获取或者设置滚动条背景色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.ScrollSliderDefaultColor">
            <summary>
            获取或者设置滚动条滑块默认颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.ScrollSliderDownColor">
            <summary>
            获取或者设置滚动条点下的颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.ScrollArrowBackColor">
            <summary>
            获取或者设置滚动条箭头的背景色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.ScrollArrowColor">
            <summary>
            获取或者设置滚动条的箭头颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.ArrowColor">
            <summary>
            获取或者设置列表项箭头的颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.ItemColor">
            <summary>
            获取或者设置列表项背景色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.FriendsMobile">
            <summary>
            获取或者设置好友是否能拖动到其他分组
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.SubItemColor">
            <summary>
            获取或者设置子项的背景色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.ItemMouseOnColor">
            <summary>
            获取或者设置当鼠标移动到列表项的颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.SubItemMouseOnColor">
            <summary>
            获取或者设置当鼠标移动到子项的颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.SubItemSelectColor">
            <summary>
            获取或者设置选中的子项的颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.VipFontColor">
            <summary>
            用户备注字体的颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListBox.ListHadOpenGroup">
            <summary>
            记录当前打开的组 便于点击时候恢复
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ChatListEventArgs.#ctor(CCWin.SkinControl.ChatListSubItem,CCWin.SkinControl.ChatListSubItem)">
            <summary>
            悬浮事件
            </summary>
            <param name="mouseonsubitem">鼠标上所悬浮的好友</param>
            <param name="selectsubitem">选中的好友</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.Clone">
            <summary>
            深拷贝
            </summary>
            <returns>深度克隆的ChatListItem</returns>
        </member>
        <member name="P:CCWin.SkinControl.ChatListItem.Text">
            <summary>
            获取或者设置列表项的显示文本
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListItem.Tag">
            <summary>
            自定义数据
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListItem.IsOpen">
            <summary>
            获取或者设置列表项是否展开
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListItem.TwinkleSubItemNumber">
            <summary>
            当前列表项下面闪烁图标的个数
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListItem.Bounds">
            <summary>
            获取列表项的显示区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListItem.OwnerChatListBox">
            <summary>
            获取列表项所在的控件
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListItem.SubItems">
            <summary>
            获取当前列表项所有子项的集合
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.Sort">
            <summary>
            对列表进行排序
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.GetOnLineNumber">
            <summary>
            获取在线人数
            </summary>
            <returns>在线人数</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.IndexOf(CCWin.SkinControl.ChatListSubItem)">
            <summary>
            获取索引位置
            </summary>
            <param name="subItem">要获取索引的子项</param>
            <returns>索引</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.Add(CCWin.SkinControl.ChatListSubItem)">
            <summary>
            添加一个子项
            </summary>
            <param name="subItem">要添加的子项</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.AddRange(CCWin.SkinControl.ChatListSubItem[])">
            <summary>
            添加一组子项
            </summary>
            <param name="subItems">要添加子项的数组</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.AddAccordingToStatus(CCWin.SkinControl.ChatListSubItem)">
            <summary>
            根据在线状态添加一个子项
            </summary>
            <param name="subItem">要添加的子项</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.Remove(CCWin.SkinControl.ChatListSubItem)">
            <summary>
            移除一个子项
            </summary>
            <param name="subItem">要移除的子项</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.RemoveAt(System.Int32)">
            <summary>
            根据索引移除一个子项
            </summary>
            <param name="index">要移除子项的索引</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.Clear">
            <summary>
            清空所有子项*有问题
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.Insert(System.Int32,CCWin.SkinControl.ChatListSubItem)">
            <summary>
            根据索引插入一个子项
            </summary>
            <param name="index">索引位置</param>
            <param name="subItem">要插入的子项</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            将集合类的子项拷贝至数组
            </summary>
            <param name="array">要拷贝的数组</param>
            <param name="index">拷贝的索引位置</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.Contains(CCWin.SkinControl.ChatListSubItem)">
            <summary>
            判断子项是否在集合内
            </summary>
            <param name="subItem">要判断的子项</param>
            <returns>是否在集合内</returns>
        </member>
        <member name="P:CCWin.SkinControl.ChatListItem.ChatListSubItemCollection.Item(System.Int32)">
            <summary>
            根据索引获取一个列表子项
            </summary>
            <param name="index">索引位置</param>
            <returns>列表子项</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItemCollection.IndexOf(CCWin.SkinControl.ChatListItem)">
            <summary>
            获取列表项所在的索引位置
            </summary>
            <param name="item">要获取的列表项</param>
            <returns>索引位置</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItemCollection.Add(CCWin.SkinControl.ChatListItem)">
            <summary>
            添加一个列表项
            </summary>
            <param name="item">要添加的列表项</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItemCollection.AddRange(CCWin.SkinControl.ChatListItem[])">
            <summary>
            添加一个列表项的数组
            </summary>
            <param name="items">要添加的列表项的数组</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItemCollection.Remove(CCWin.SkinControl.ChatListItem)">
            <summary>
            移除一个列表项
            </summary>
            <param name="item">要移除的列表项</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItemCollection.RemoveAt(System.Int32)">
            <summary>
            根据索引位置删除一个列表项
            </summary>
            <param name="index">索引位置</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItemCollection.Clear">
            <summary>
            清空所有列表项
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItemCollection.Insert(System.Int32,CCWin.SkinControl.ChatListItem)">
            <summary>
            根据索引位置插入一个列表项
            </summary>
            <param name="index">索引位置</param>
            <param name="item">要插入的列表项</param>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItemCollection.Contains(CCWin.SkinControl.ChatListItem)">
            <summary>
            判断一个列表项是否在集合内
            </summary>
            <param name="item">要判断的列表项</param>
            <returns>是否在列表项</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListItemCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            将列表项的集合拷贝至一个数组
            </summary>
            <param name="array">目标数组</param>
            <param name="index">拷贝的索引位置</param>
        </member>
        <member name="P:CCWin.SkinControl.ChatListItemCollection.Item(System.Int32)">
            <summary>
            根据索引获取一个列表项
            </summary>
            <param name="index">索引位置</param>
            <returns>列表项</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListSubItem.Clone">
            <summary>
            深拷贝
            </summary>
            <returns>深度克隆的ChatListSubItem</returns>
        </member>
        <member name="M:CCWin.SkinControl.ChatListSubItem.GetDarkImage">
            <summary>
            获取当前用户的黑白头像
            </summary>
            <returns>黑白头像</returns>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.ID">
            <summary>
            获取或者设置用户账号
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.Tag">
            <summary>
            与对象关联的用户定义数据
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.PlatformTypes">
            <summary>
            获取或设置用户登录平台
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.NicName">
            <summary>
            获取或者设置用户昵称
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.DisplayName">
            <summary>
            获取或者设置用户备注名称
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.PersonalMsg">
            <summary>
            获取或者设置用户签名信息
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.QQShow">
            <summary>
            获取或设置用户QQ秀
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.IpAddress">
            <summary>
            获取或者设置用户IP地址
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.UpdPort">
            <summary>
            获取或者设置用户Upd端口
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.TcpPort">
            <summary>
            获取或者设置用户Tcp端口
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.HeadImage">
            <summary>
            获取或者设置用户头像
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.Status">
            <summary>
            获取或者设置用户当前状态
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.IsVip">
            <summary>
            是否是VIP
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.IsTwinkle">
            <summary>
            获取或者设置是否闪动
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.Bounds">
            <summary>
            获取列表子项显示区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.HeadRect">
            <summary>
            获取头像显示区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ChatListSubItem.OwnerListItem">
            <summary>
            获取当前列表子项所在的列表项
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.DragListEventArgs.#ctor(CCWin.SkinControl.ChatListSubItem,CCWin.SkinControl.ChatListSubItem)">
            <summary>
            拖动事件
            </summary>
            <param name="QSubItem">拖动前好友</param>
            <param name="HSubItem">拖动后好友</param>
        </member>
        <member name="T:CCWin.SkinControl.SkinProgressIndicator">
            <summary>
            圆形进度条
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinProgressIndicator.#ctor">
            <summary>
            无参构造
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinProgressIndicator.Start">
            <summary>
            Starts the animation.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinProgressIndicator.Stop">
            <summary>
            Stops the animation.
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinProgressIndicator.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinProgressIndicator.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinProgressIndicator.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressIndicator.CircleColor">
            <summary>
            获取或设置圆形进度条的颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressIndicator.AutoStart">
            <summary>
            获取或设置一个值，指示是否应自动启动动画
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressIndicator.CircleSize">
            <summary>
            获取或设置小圆形的大小，从0.1到1。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressIndicator.AnimationSpeed">
            <summary>
            获取或设置动画速度。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressIndicator.NumberOfCircles">
            <summary>
            获取或设置用于在动画圈里的速率圈数。
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"><c>NumberOfCircles</c> is out of range.</exception>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressIndicator.NumberOfVisibleCircles">
            <summary>
            获取或设置用于在动画圈的残影数量。
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"><c>NumberOfCircles</c> is out of range.</exception>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressIndicator.Rotation">
            <summary>
            获取或设置一个值，指示是否应顺时针或逆时针旋转。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressIndicator.Percentage">
            <summary>
            获取或设置百分比值。
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException"><c>Percentage</c> is out of range.</exception>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressIndicator.ShowPercentage">
            <summary>
            获取或设置一个值，指示是否应显示百分比值。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressIndicator.ShowText">
            <summary>
            获取或设置一个值，指示是否控制要显示的文字。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressIndicator.TextDisplay">
            <summary>
            获取或设置将在控件显示的文本显示模式。
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.RotationType">
            <summary>
            枚举用于指示旋转方向的控制。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.RotationType.Clockwise">
            <summary>
            指出应顺时针旋转。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.RotationType.CounterClockwise">
            <summary>
            表明要逆时针旋转。
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.TextDisplayModes">
            <summary>
            此枚举用于选择什么样的文本应该在控件显示。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.TextDisplayModes.None">
            <summary>
            没有文本将显示在控件。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.TextDisplayModes.Percentage">
            <summary>
            控件将显示属性值的百分比。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.TextDisplayModes.Text">
            <summary>
            控件将显示文本属性的值。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.TextDisplayModes.Both">
            <summary>
            控件将显示值的文本和百分比特性。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinAlphaWaterTextBox._waterFont">
            <summary>
            用于显示水印文字的字体
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinAlphaWaterTextBox._waterText">
            <summary>
            水印文字
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinAlphaWaterTextBox._waterColor">
            <summary>
            水印文字的颜色
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinAlphaWaterTextBox.WmPaintWater(System.Drawing.Graphics)">
            <summary>
            绘制水印
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinAlphaWaterTextBox.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinAnimatorImg.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimatorImg.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinAnimatorImg.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinButton.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinButton.HuiseColor(System.Drawing.Color)">
            <summary>
            将颜色灰色度处理
            </summary>
            <param name="c"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.SkinButton.SetCreate(System.Drawing.Rectangle,System.Drawing.Bitmap)">
            <summary>
            设置Region区域
            </summary>
            <param name="rc"></param>
            <param name="btm"></param>
            <returns></returns>
        </member>
        <member name="F:CCWin.SkinControl.SkinButton.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinButton.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinButton.BorderInflate">
            <summary>
            边框放大指定变量
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinButton.InheritColor">
            <summary>
            是否继承所在窗体的色调
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinButton.FadeGlow">
            <summary>
            是否开启动画渐变效果
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinButton.Radius">
            <summary>
            圆角大小
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinButton.ControlState">
            <summary>
            控件状态
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinButton.Palace">
            <summary>
            是否开启九宫绘图
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinButton.Create">
            <summary>
            是否开启不规则控件
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinButton.BackRectangle">
            <summary>
            九宫绘画区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinButton.MouseBack">
            <summary>
            悬浮时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinButton.DownBack">
            <summary>
            点击时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinButton.NormlBack">
            <summary>
            初始时
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.StopStates">
            <summary>
            控件的状态。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.StopStates.Normal">
            <summary>
             正常。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.StopStates.Hover">
            <summary>
            鼠标进入。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.StopStates.Pressed">
            <summary>
            鼠标按下。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.StopStates.NoStop">
            <summary>
            停止Stop状态
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinCheckBox.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinCheckBox.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinCheckBox.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCheckBox.BaseColor">
            <summary>
            非图片绘制时CheckBox色调
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCheckBox.DefaultCheckButtonWidth">
            <summary>
            选择框大小
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCheckBox.ControlState">
            <summary>
            控件状态
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCheckBox.MouseBack">
            <summary>
            悬浮时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCheckBox.DownBack">
            <summary>
            点击时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCheckBox.NormlBack">
            <summary>
            初始时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCheckBox.SelectedMouseBack">
            <summary>
            悬浮时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCheckBox.SelectedDownBack">
            <summary>
            点击时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCheckBox.SelectedNormlBack">
            <summary>
            初始时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCheckBox.LightEffect">
            <summary>
            是否绘制发光字体
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCheckBox.LightEffectBack">
            <summary>
            发光字体背景色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinCheckBox.LightEffectWidth">
            <summary>
            光圈大小
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinColorSelectPanel.SelectedColor">
            <summary>
            获取或设置 <see cref="T:CCWin.SkinControl.SkinColorSelectPanel"/> 选择的颜色。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinColorSelectPanel.SelectMode">
            <summary>
            获取或设置 <see cref="T:CCWin.SkinControl.SkinColorSelectPanel"/> 颜色选择模式。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinComboBox._waterText">
            <summary>
            水印文字
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinComboBox._waterColor">
            <summary>
            水印的颜色
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinDataGridView.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinDataGridView.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinDataGridView.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinDataGridView.TitlePalace">
            <summary>
            标题背景是否开启九宫绘图
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinDataGridView.TitleBackRectangle">
            <summary>
            标题背景九宫绘画区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinDataGridView.TitleBack">
            <summary>
            标题背景
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinGroupBox.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinGroupBox.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinGroupBox.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinGroupBox.Radius">
            <summary>
            圆角大小
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinGroupBox.TitleRadius">
            <summary>
            圆角大小
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.PassKey.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.PassKey.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinControl.PassKey.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ArtTextStyle.None">
            <summary>
            正常样式。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ArtTextStyle.Border">
            <summary>
            边框样式。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ArtTextStyle.Relievo">
            <summary>
            浮雕样式。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ArtTextStyle.Forme">
            <summary>
            印版样式。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ArtTextStyle.Anamorphosis">
            <summary>
            渐变样式
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinListBox.Radius">
            <summary>
            圆角大小
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinListBox.Palace">
            <summary>
            是否开启九宫绘图
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinListBox.BackRectangle">
            <summary>
            九宫绘画区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinListBox.Back">
            <summary>
            背景
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinListBox.ItemRadius">
            <summary>
            圆角大小
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinPanel.UpdateRadius">
            <summary>
            更新圆角
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinPanel.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinPanel.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinPanel.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinPanel.ControlState">
            <summary>
            控件状态
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinPanel.Palace">
            <summary>
            是否开启九宫绘图
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinPanel.BackRectangle">
            <summary>
            九宫绘画区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinPanel.MouseBack">
            <summary>
            悬浮时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinPanel.DownBack">
            <summary>
            点击时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinPanel.NormlBack">
            <summary>
            初始时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinPanel.Radius">
            <summary>
            圆角大小
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinPictureBox.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinPictureBox.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinPictureBox.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ColorBox.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ColorBox.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinControl.ColorBox.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.FrmCapture.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.FrmCapture.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinControl.FrmCapture.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.FrmCapture.IsCaptureCursor">
            <summary>
            获取或设置是否捕获鼠标
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.FrmCapture.IsFromClipBoard">
            <summary>
            获取或设置是否从剪切板获取图像
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.FrmCapture.ImgProcessBoxIsShowInfo">
            <summary>
            获取或设置是否显示图像信息
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.FrmCapture.ImgProcessBoxDotColor">
            <summary>
            获取或设置操作框点的颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.FrmCapture.ImgProcessBoxLineColor">
            <summary>
            获取或设置操作框边框颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.FrmCapture.ImgProcessBoxMagnifySize">
            <summary>
            获取或设置放大图形的原始尺寸
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.FrmCapture.ImgProcessBoxMagnifyTimes">
            <summary>
            获取或设置放大图像的倍数
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.FrmCapture.BmpLayerCurrent">
            <summary>
            控件所截图的图片
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.FrmOut.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.FrmOut.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinControl.FrmOut.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.FrmSize.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.FrmSize.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinControl.FrmSize.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ImageProcessBox.ClearDraw">
            <summary>
            清空所有操作
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ImageProcessBox.SetSelectRect(System.Drawing.Rectangle)">
            <summary>
            手动设置一个块选中区域
            </summary>
            <param name="rect">要选中区域</param>
        </member>
        <member name="M:CCWin.SkinControl.ImageProcessBox.SetSelectRect(System.Drawing.Point,System.Drawing.Size)">
            <summary>
            手动设置一个块选中区域
            </summary>
            <param name="pt">要选中区域的坐标</param>
            <param name="se">要选中区域的大小</param>
        </member>
        <member name="M:CCWin.SkinControl.ImageProcessBox.SetSelectRect(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            手动设置一个块选中区域
            </summary>
            <param name="x">要选中区域的x坐标</param>
            <param name="y">要选中区域的y坐标</param>
            <param name="w">要选中区域的宽度</param>
            <param name="h">要选中区域的高度</param>
        </member>
        <member name="M:CCWin.SkinControl.ImageProcessBox.SetInfoPoint(System.Drawing.Point)">
            <summary>
            手动设置信息显示的位置
            </summary>
            <param name="pt">要显示的位置</param>
        </member>
        <member name="M:CCWin.SkinControl.ImageProcessBox.SetInfoPoint(System.Int32,System.Int32)">
            <summary>
            手动设置信息显示的位置
            </summary>
            <param name="x">要显示位置的x坐标</param>
            <param name="y">要显示位置的y坐标</param>
        </member>
        <member name="M:CCWin.SkinControl.ImageProcessBox.GetResultBmp">
            <summary>
            获取操作框内的图像
            </summary>
            <returns>结果图像</returns>
        </member>
        <member name="F:CCWin.SkinControl.ImageProcessBox.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ImageProcessBox.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinControl.ImageProcessBox.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.BaseImage">
            <summary>
            获取或设置用于被操作的图像
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.DotColor">
            <summary>
            获取或设置操作框点的颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.LineColor">
            <summary>
            获取或设置操作框线条的颜色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.SelectedRectangle">
            <summary>
            获取当前选中的区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.MagnifySize">
            <summary>
            获取或设置放大图像的原图大小尺寸
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.MagnifyTimes">
            <summary>
            获取或设置图像放大的倍数
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.IsDrawOperationDot">
            <summary>
            获取或设置是否绘制操作框点
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.IsSetClip">
            <summary>
            获取或设置是否限制鼠标操作区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.IsShowInfo">
            <summary>
            获取或设置是否绘制信息展示
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.AutoSizeFromImage">
            <summary>
            获取或设置是否根据图像大小自动调整控件尺寸
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.IsDrawed">
            <summary>
            获取当前是否绘制的有区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.IsStartDraw">
            <summary>
            获取当前是否开始绘制
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.IsMoving">
            <summary>
            获取当前操作框是否正在移动
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.ImageProcessBox.CanReset">
            <summary>
            获取或设置操作框是否锁定
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.ToolButton.components">
            <summary> 
            Required designer variable.
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.ToolButton.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:CCWin.SkinControl.ToolButton.InitializeComponent">
            <summary> 
            Required method for Designer support - do not modify 
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressBar.Create">
            <summary>
            是否开启不规则控件
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinProgressBar.FormatString">
            <summary>
            显示进度完成信息的格式化字符串。
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.SkinProgressBar.TxtFormat">
            <summary>
            文本的格式
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinProgressBar.TxtFormat.None">
            <summary>
             没有文本。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinProgressBar.TxtFormat.Percentage">
            <summary>
            百分比。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinProgressBar.TxtFormat.Proportion">
            <summary>
            比例。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRadioButton.BaseColor">
            <summary>
            非图片绘制时RadioButton色调
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRadioButton.DefaultRadioButtonWidth">
            <summary>
            选择框大小
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRadioButton.MouseBack">
            <summary>
            悬浮时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRadioButton.DownBack">
            <summary>
            点击时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRadioButton.NormlBack">
            <summary>
            初始时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRadioButton.SelectedMouseBack">
            <summary>
            悬浮时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRadioButton.SelectedDownBack">
            <summary>
            点击时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRadioButton.SelectedNormlBack">
            <summary>
            初始时
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRadioButton.LightEffect">
            <summary>
            是否绘制发光字体
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRadioButton.LightEffectBack">
            <summary>
            发光字体背景色
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinRadioButton.LightEffectWidth">
            <summary>
            光圈大小
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.SkinEventsCollection">
            <summary>
            带事件的实现IList接口的集合
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinScrollBarBase.SetNewTheme(CCWin.SkinControl.SkinScrollBarThemeBase)">
            <summary>
            设置主题
            </summary>
            <param name="xtheme">主题</param>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarBase.Value">
            <summary>
            滚动框位置表示的值。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarBase.Minimum">
            <summary>
            可滚动范围的下限值。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarBase.Maximum">
            <summary>
            可滚动范围的上限值。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarBase.SmallChange">
            <summary>
            当用户单击滚动箭头或按箭头键时，滚动框位置变动的幅度。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarBase.LargeChange">
            <summary>
            当用户单击滚动条或按 Page Up 或 Page Down 键时，滚动框位置变动的幅度。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarBase.MiddleButtonLengthPercentage">
            <summary>
            滑块的长度
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarBase.XTheme">
            <summary>
            当前主题
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarBase.MdlButtonTheme">
            <summary>
            按钮主题
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarBase.BackgroundRadius">
            <summary>
            滚动条控件整体的圆角大小
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarThemeBase.MiddleButtonOutterSpace1">
            <summary>
            在可滚动方向上的两头的空白
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarThemeBase.MiddleButtonOutterSpace2">
            <summary>
            在不可滚动方向上的两头的空白
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarThemeBase.SideButtonLength">
            <summary>
            滚动条两头的按钮的宽度(HScrollBar)或高度(VScrollBar)
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarThemeBase.BestUndirectLen">
            <summary>
            在非可移动方向上的最佳长度，对 vscroll 来说是 width, 
            对 hscroll 来说是 height
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarThemeBase.DrawLinesInMiddleButton">
            <summary>
            是否在滚动块上画3条线
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarThemeBase.BackgroundRadius">
            <summary>
            滚动条控件整体的圆角大小
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinScrollBarThemeBase.DrawExtraMiddleLine">
            <summary>
            是否在滚动条中部画连接两头SideButton的线，主要用于圆形的SideButton
            </summary>
        </member>
        <member name="T:CCWin.SkinClass.RectHelper">
            <summary>
            该类提供一些静态方法来处理Rectangle的常用操作
            </summary>
        </member>
        <member name="M:CCWin.SkinClass.RectHelper.IncreaseWH(System.Drawing.Rectangle)">
            <summary>
            将rect的Width和Height分别加1，并返回新的结果
            </summary>
            <param name="rect">原始的方框</param>
            <returns>增加宽高后的方框</returns>
        </member>
        <member name="M:CCWin.SkinClass.RectHelper.DecreaseWH(System.Drawing.Rectangle)">
            <summary>
            将rect的Width和Height分别减1，并返回新的结果
            </summary>        
        </member>
        <member name="M:CCWin.SkinClass.RectHelper.EqualOrFullyContains(System.Drawing.Rectangle,System.Drawing.Rectangle)">
            <summary>
            判断rect1是否等于rect2, 或完全包含了rect2
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.WLButton">
            <summary>
            WL是winless的缩写，即该Button类是无句柄的
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.WLControlBase.CtlSize">
            <summary>
            获取或设置Winless控件的大小
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.WLControlBase.Tag">
            <summary>
            获取或设置控件的附加数据
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.WLControlBase.CtlLeft">
            <summary>
            获取Winless控件的位置的X坐标
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.WLControlBase.CtlTop">
            <summary>
            获取Winless控件的位置的Y坐标
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.WLButtonBase.Capture">
            <summary>
            获取控件是否捕获了鼠标
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.WLButtonBase.State">
            <summary>
            获取或设置按钮的状态
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.WLButtonBase.Text">
            <summary>
            获取或设置控件的文本
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.WLButton.RestrictedBounds">
            <summary>
            获取或设置是否将绘制完全限制在指定的区域内
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.WLButton.ClickSendBackOject">
            <summary>
            用于在click事件中传回数据
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.WLButton.DrawForePathTwice">
            <summary>
            画两次可以加深颜色
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.WLContainerBase.capturedControl">
            <summary>
            表示捕获了鼠标焦点的控件，即鼠标在这个控件上点击且未松开
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.WLContainerBase.lastMouseMoveControl">
            <summary>
            表示最近一次鼠标在上面正常移动（按钮未按下）的控件
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.WLContainerBase.WLControls">
            <summary>
            获取子控件集合
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.WLScrollBar">
            <summary>
            Winless scrollbar
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.WLScrollBar.OnValueChanged(System.EventArgs)">
            <summary>
            引发 ValueChanged 事件
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.WLScrollBar.ResetMiddleButtonPosition">
            <summary>
            根据当前Value值重新设置滚动块的位置
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.WLScrollBar.HasEnoughRoomForMiddleButton">
            <summary>
            判断当前是否有足够的空间来显示滚动块
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.WLScrollBar.Refresh">
            <summary>
            刷新
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.WLScrollBar.DealMiddleButtonMove(System.Int32)">
            <summary>
            移动滚动块时调用该方法来设置新Value
            </summary>
            <param name="moveDelta">移动偏移量</param>
        </member>
        <member name="M:CCWin.SkinControl.WLScrollBar.ButtonsIni">
            <summary>
            sidebutton1/2, middlebutton 初始化
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.WLScrollBar.SetNewTheme(CCWin.SkinControl.SkinScrollBarThemeBase)">
            <summary>
            给ScrollBar设置一个新的主题
            </summary>
            <param name="xtheme">主题实例</param>
        </member>
        <member name="M:CCWin.SkinControl.WLScrollBar.ValueAdd(System.Int32)">
            <summary>
            将Value值加上指定的量
            </summary>
            <param name="amount">需要增加的量，可正可负</param>
        </member>
        <member name="M:CCWin.SkinControl.WLScrollBar.SmallAdd">
            <summary>
            将当前Value值加上SmallChange量
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.WLScrollBar.SmallSub">
            <summary>
            将当前Value值减去SmallChange量
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.WLScrollBar.LargeAdd">
            <summary>
            将当前Value值加上LargeChange量
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.WLScrollBar.LargeSub">
            <summary>
            将当前Value值减去LargeChange量
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.WLScrollBar.LocationResult">
            <summary>
            指示鼠标位于哪个区域
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinToolStrip.OnCreateControl">
            <summary>
            初始化绑定的Tab
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinToolStrip.OnItemAdded(System.Windows.Forms.ToolStripItemEventArgs)">
            <summary>
            每添加一个ToolStripButton都给他加一个CheckedChanged事件
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinToolStrip.btn_CheckedChanged(System.Object,System.EventArgs)">
            <summary>
            每一项Btn的CheckedChanged事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinToolStrip.bindTabControl_SelectedIndexChanged(System.Object,System.EventArgs)">
            <summary>
            实现TabControl切换Tabpage时也切换Item按钮
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinToolStrip.OnItemClicked(System.Windows.Forms.ToolStripItemClickedEventArgs)">
            <summary>
            实现按钮点击时切换TabPage
            </summary>
            <param name="e"></param>
        </member>
        <member name="F:CCWin.SkinControl.SkinTabControlDesigner._verbs">
            <summary>
            
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinTabControlDesigner._designerHost">
            <summary>
            
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinTabControlDesigner._selectionService">
            <summary>
            
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.TabPageEventArgs.#ctor(System.Windows.Forms.TabPage)">
            <summary>
            悬浮事件
            </summary>
            <param name="selectsubitem">选中的好友</param>
        </member>
        <member name="P:CCWin.SkinControl.TabPageEventArgs.ColseTabPage">
            <summary>
            关闭的容器
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinTabControl._btnArrowRect">
            <summary>
            选项卡箭头区域
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinTabControl._isFocus">
            <summary>
            是否获取了焦点
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinTabControl.imgTxtOffset">
            <summary>
            Page图标文本整体偏移
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinTabControl._closeRect">
            <summary>
            关闭按钮区域
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinTabControl.pageCloseLeftToRight">
            <summary>
            Page关闭按钮位置是否在右。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinTabControl.pageCloseVisble">
            <summary>
            Page是否开启关闭按钮
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinTabControl.GetTabRectClient(System.Int32)">
            <summary>
            根据index获取Tab标签真实大小
            </summary>
            <param name="index"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.SkinControl.SkinTabControl.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinTabControl.DrawImage(System.Drawing.Graphics,System.Drawing.Image,System.Drawing.Rectangle)">
            <summary>
            绘图
            </summary>
            <param name="g"></param>
            <param name="image"></param>
            <param name="rect"></param>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.Animator">
            <summary>
            动画组件
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.IcoStateSwitch">
            <summary>
            是否开启Tab图标三效状态切换
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.Interval">
            <summary>
            帧之间时间间隔（MS）
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.ItemStretch">
            <summary>
            选项卡标签大小是否可以自动拉伸
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.ImgSize">
            <summary>
            Page图标大小
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.HeadBack">
            <summary>
            Head标签背景
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.HeadPalace">
            <summary>
            Head是否开启九宫绘图
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.HeadBackRectangle">
            <summary>
            Head九宫绘画区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.PagePalace">
            <summary>
            Page是否开启九宫绘图
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.PageBackRectangle">
            <summary>
            Page九宫绘画区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.PageArrowPalace">
            <summary>
            Page是否开启九宫绘图
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.PageArrowRectangle">
            <summary>
            Page九宫绘画区域
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabControl.AnimationStart">
            <summary>
            是否开启动画切换效果
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabPage.TabItemImage">
            <summary>
            当前Tab选项卡图标
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTabPage.IsSelectTab">
            <summary>
            是否能选中当前项
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.CloneObj.Clone(System.Windows.Forms.TabPage)">
            <summary>
            克隆一个TabPage
            </summary>
            <param name="Value">素体</param>
            <returns>衍生体</returns>
        </member>
        <member name="M:CCWin.SkinControl.SkinTextBox.InitEvents">
            <summary>
            加载事件
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinTextBox.flag">
            <summary>
            偏移文本框
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinTextBox.BaseText_MouseLeave(System.Object,System.EventArgs)">
            <summary>
            
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinTextBox.BaseText_MouseEnter(System.Object,System.EventArgs)">
            <summary>
            
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinTextBox.OnSizeChanged(System.EventArgs)">
            <summary>
            当文本框的大小发生改变时，将文本框的类型换成多行文本
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinTextBox.OnPaint(System.Windows.Forms.PaintEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinTextBox.OnMouseEnter(System.EventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinTextBox.BaseText_MouseDown(System.Object,System.Windows.Forms.MouseEventArgs)">
            <summary>
            文本框的按下事件
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinTextBox.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
            <summary>
            文本框边框的按下事件
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinTextBox.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:CCWin.SkinControl.SkinTextBox.OnMouseLeave(System.EventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="F:CCWin.SkinControl.SkinTextBox.components">
            <summary> 
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinTextBox.Dispose(System.Boolean)">
            <summary> 
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinTextBox.InitializeComponent">
            <summary> 
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.MaxLength">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.Multiline">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.IsPasswordChat">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.ReadOnly">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.IsSystemPasswordChar">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.WordWrap">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.Font">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.ForeColor">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.Lines">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.ScrollBars">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.TextAlign">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.WaterText">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.WaterColor">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.MouseState">
            <summary>
            当前文本框状态
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.IconMouseState">
            <summary>
            当前ICO图标状态
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTextBox.IconRect">
            <summary>
            图标的绘制区域
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinWaterTextBox._waterText">
            <summary>
            水印文字
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinWaterTextBox._waterColor">
            <summary>
            水印文字的颜色
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinWaterTextBox.WmPaintWater(System.Windows.Forms.Message@)">
            <summary>
            绘制水印
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinWaterTextBox.WndProc(System.Windows.Forms.Message@)">
            <summary>
            
            </summary>
            <param name="m"></param>
        </member>
        <member name="P:CCWin.SkinControl.SkinWaterTextBox.WaterText">
            <summary>
            
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinWaterTextBox.WaterColor">
            <summary>
            
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.TrackBarBase">
            <summary>
            实现标准的 Windows 跟踪条要求的基本功能。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.TrackBarBase.#ctor">
            <summary>
            实现标准的 Windows 跟踪条要求的基本功能。
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTrackBar.Bar">
            <summary>
            滑块
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTrackBar.Track">
            <summary>
            滑块条
            </summary>
        </member>
        <member name="P:CCWin.SkinControl.SkinTrackBar.BackRectangle">
            <summary>
            滑块条九宫绘画区域
            </summary>
        </member>
        <member name="T:CCWin.SkinControl.CollapsePanel">
            <summary>
            点击SplitContainer控件收缩按钮时隐藏的Panel。
            </summary>
        </member>
        <member name="F:CCWin.SkinControl.SkinTreeView.components">
            <summary>
            必需的设计器变量。
            </summary>
        </member>
        <member name="M:CCWin.SkinControl.SkinTreeView.Dispose(System.Boolean)">
            <summary>
            清理所有正在使用的资源。
            </summary>
            <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        </member>
        <member name="M:CCWin.SkinControl.SkinTreeView.InitializeComponent">
            <summary>
            设计器支持所需的方法 - 不要
            使用代码编辑器修改此方法的内容。
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.AC.AC_SRC_OVER">
            <summary>
            currentlly defined blend function.
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.AC.AC_SRC_ALPHA">
            <summary>
            alpha format flags.
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.AW.AW_HOR_POSITIVE">
            <summary>
            从左到右显示
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.AW.AW_HOR_NEGATIVE">
            <summary>
            从右到左显示
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.AW.AW_VER_POSITIVE">
            <summary>
            从上到下显示
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.AW.AW_VER_NEGATIVE">
            <summary>
            从下到上显示
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.AW.AW_CENTER">
            <summary>
            若使用了AW_HIDE标志，则使窗口向内重叠，即收缩窗口；否则使窗口向外扩展，即展开窗口
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.AW.AW_HIDE">
            <summary>
            隐藏窗口，缺省则显示窗口
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.AW.AW_ACTIVATE">
            <summary>
            激活窗口。在使用了AW_HIDE标志后不能使用这个标志
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.AW.AW_SLIDE">
            <summary>
            使用滑动类型。缺省则为滚动动画类型。当使用AW_CENTER标志时，这个标志就被忽略
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.AW.AW_BLEND">
            <summary>
            透明度从高到低
            </summary>
        </member>
        <member name="T:CCWin.Win32.Const.CDDS">
            <summary>
            drawstage flags
            values under 0x00010000 are reserved for global custom draw values.
            above that are for specific controls
            </summary>
        </member>
        <member name="T:CCWin.Win32.Const.CDRF">
            <summary>
            custom draw return flags
            values under 0x00010000 are reserved for global custom draw values.
            above that are for specific controls
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.DI.DI_MASK">
            <summary>
            Draws the icon or cursor using the mask.
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.DI.DI_IMAGE">
            <summary>
            Draws the icon or cursor using the image.
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.DI.DI_NORMAL">
            <summary>
            Combination of DI_IMAGE and DI_MASK.
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.DI.DI_COMPAT">
            <summary>
            Draws the icon or cursor using the system default image rather than the user-specified image. 
            For more information, see About Cursors. Windows NT4.0 and later: This flag is ignored.
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.DI.DI_DEFAULTSIZE">
            <summary>
            Draws the icon or cursor using the width and height specified by the system metric values for cursors or icons, 
            if the cxWidth and cyWidth parameters are set to zero. If this flag is not specified and cxWidth and cyWidth are set to zero, 
            the function uses the actual resource size. 
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.DI.DI_NOMIRROR">
            <summary>
            Windows XP: Draws the icon as an unmirrored icon. By default, the icon is drawn as a mirrored icon if hdc is mirrored.
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTERROR">
            <summary>
            On the screen background or on a dividing line between windows 
            (same as HTNOWHERE; except that the DefWindowProc function produces a system beep to indicate an error).
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTTRANSPARENT">
            <summary>
            In a window currently covered by another window in the same thread 
            (the message will be sent to underlying windows in the same thread until one of them returns a code that is not HTTRANSPARENT).
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTNOWHERE">
            <summary>
            On the screen background or on a dividing line between windows.
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTCLIENT">
            <summary>In a client area.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTCAPTION">
            <summary>In a title bar.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTSYSMENU">
            <summary>In a window menu or in a Close button in a child window.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTGROWBOX">
            <summary>In a size box (same as HTSIZE).</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTMENU">
            <summary>In a menu.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTHSCROLL">
            <summary>In a horizontal scroll bar.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTVSCROLL">
            <summary>In the vertical scroll bar.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTMINBUTTON">
            <summary>In a Minimize button.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTMAXBUTTON">
            <summary>In a Maximize button.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTLEFT">
            <summary>In the left border of a resizable window 
            (the user can click the mouse to resize the window horizontally).</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTRIGHT">
            <summary>
            In the right border of a resizable window 
            (the user can click the mouse to resize the window horizontally).
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTTOP">
            <summary>In the upper-horizontal border of a window.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTTOPLEFT">
            <summary>In the upper-left corner of a window border.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTTOPRIGHT">
            <summary>In the upper-right corner of a window border.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTBOTTOM">
            <summary>	In the lower-horizontal border of a resizable window 
            (the user can click the mouse to resize the window vertically).</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTBOTTOMLEFT">
            <summary>In the lower-left corner of a border of a resizable window 
            (the user can click the mouse to resize the window diagonally).</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTBOTTOMRIGHT">
            <summary>	In the lower-right corner of a border of a resizable window 
            (the user can click the mouse to resize the window diagonally).</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTBORDER">
            <summary>In the border of a window that does not have a sizing border.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTCLOSE">
            <summary>In a Close button.</summary>
        </member>
        <member name="F:CCWin.Win32.Const.HITTEST.HTHELP">
            <summary>In a Help button.</summary>
        </member>
        <member name="T:CCWin.Win32.Const.HWND">
            <summary>
            Use for setwindowpos.
            </summary>
        </member>
        <member name="T:CCWin.Win32.Const.ICC">
            <summary>
            INITCOMMONCONTROLSEX 结构的 dwICC 字段使用的常量。
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.ICC.ICC_LISTVIEW_CLASSES">
            <summary>
            listview, header
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.ICC.ICC_TREEVIEW_CLASSES">
            <summary>
            treeview, tooltips
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.ICC.ICC_BAR_CLASSES">
            <summary>
            注册工具栏、状态栏、Trackbar和Tooltip类。
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.ICC.ICC_TAB_CLASSES">
            <summary>
            tab, tooltips
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.ICC.ICC_UPDOWN_CLASS">
            <summary>
            updown
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.ICC.ICC_PROGRESS_CLASS">
            <summary>
            progress
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.ICC.ICC_HOTKEY_CLASS">
            <summary>
            hotkey
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.ICC.ICC_ANIMATE_CLASS">
            <summary>
            animate
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.ICC.ICC_DATE_CLASSES">
            <summary>
            month picker, date picker, time picker, updown
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.ICC.ICC_USEREX_CLASSES">
            <summary>
            comboex
            </summary>
        </member>
        <member name="F:CCWin.Win32.Const.ICC.ICC_COOL_CLASSES">
             <summary>
            注册Rebar类。
             </summary>
        </member>
        <member name="T:CCWin.Win32.Const.NM">
            <summary>
            Generic WM_NOTIFY notification codes 
            </summary>
        </member>
        <member name="T:CCWin.Win32.Const.SS">
            <summary>
            Static Style.
            </summary>
        </member>
        <member name="T:CCWin.Win32.Const.SWP">
            <summary>
            SetWindowPos Flags.
            </summary>
        </member>
        <member name="T:CCWin.Win32.Const.TBM">
            <summary>
            trackbar messages.
            </summary>
        </member>
        <member name="T:CCWin.Win32.Const.TTI">
            <summary>
            ToolTip Icons possible wParam values for TTM_SETTITLE message.
            </summary>
        </member>
        <member name="T:CCWin.Win32.Const.TTS">
            <summary>
            styles
            </summary>
        </member>
        <member name="T:CCWin.Win32.Const.WVR">
            <summary>
            WM_NCCALCSIZE "window valid rect" return values.
            </summary>
        </member>
        <member name="M:CCWin.Win32.DwmApi.DwmEnableBlurBehindWindow(System.IntPtr,CCWin.Win32.DwmApi.DWM_BLURBEHIND)">
            <summary>
            允许在指定的窗口模糊效果
            </summary>
            <param name="hWnd"></param>
            <param name="pBlurBehind"></param>
        </member>
        <member name="M:CCWin.Win32.DwmApi.DwmExtendFrameIntoClientArea(System.IntPtr,CCWin.Win32.DwmApi.MARGINS)">
            <summary>
            扩展到客户端区域的窗框
            </summary>
            <param name="hWnd"></param>
            <param name="pMargins"></param>
        </member>
        <member name="M:CCWin.Win32.DwmApi.DwmIsCompositionEnabled">
            <summary>
            获取一个值，指示是否启用DWM的组成。 应用程序可以收听组成状态变化的处理
            </summary>
            <returns></returns>
        </member>
        <member name="M:CCWin.Win32.DwmApi.DwmGetColorizationColor(System.Int32@,System.Boolean@)">
            <summary>
            检索DWM的玻璃成分中使用当前的颜色。.应用程序可以听颜色的变化， 处理 WM_DWMCOLORIZATIONCOLORCHANGED通知
            </summary>
            <param name="pcrColorization"></param>
            <param name="pfOpaqueBlend"></param>
        </member>
        <member name="M:CCWin.Win32.DwmApi.DwmEnableComposition(System.Boolean)">
            <summary>
            启用或禁用DWM的组成
            </summary>
            <param name="bEnable"></param>
        </member>
        <member name="M:CCWin.Win32.DwmApi.DwmRegisterThumbnail(System.IntPtr,System.IntPtr)">
            <summary>
            创建目标和源窗口之间的DWM缩略图关系
            </summary>
            <param name="dest"></param>
            <param name="source"></param>
            <returns></returns>
        </member>
        <member name="M:CCWin.Win32.DwmApi.DwmUnregisterThumbnail(System.IntPtr)">
            <summary>
            移除DWM缩略图关系DwmRegisterThumbnail功能
            </summary>
            <param name="hThumbnail"></param>
        </member>
        <member name="M:CCWin.Win32.DwmApi.DwmUpdateThumbnailProperties(System.IntPtr,CCWin.Win32.DwmApi.DWM_THUMBNAIL_PROPERTIES)">
            <summary>
            更新的属性为DWM缩略图。
            </summary>
            <param name="hThumbnail"></param>
            <param name="props"></param>
        </member>
        <member name="M:CCWin.Win32.DwmApi.DwmQueryThumbnailSourceSize(System.IntPtr,System.Drawing.Size@)">
            <summary>
            检索源DWM缩略图的大小
            </summary>
            <param name="hThumbnail"></param>
            <param name="size"></param>
        </member>
        <member name="M:CCWin.Win32.NativeMethods.RegisterHotKey(System.IntPtr,System.Int32,CCWin.SkinControl.KeyModifiers,System.Windows.Forms.Keys)">
            <summary>
            热键定义
            </summary>
            <param name="hWnd">要定义热键的窗口的句柄 </param>
            <param name="id">定义热键ID（不能与其它ID重复）</param>
            <param name="fsModifiers">标识热键是否在按Alt、Ctrl、Shift、Windows等键时才会生效 </param>
            <param name="vk">定义热键的内容 </param>
            <returns></returns>
        </member>
        <member name="M:CCWin.Win32.NativeMethods.UnregisterHotKey(System.IntPtr,System.Int32)">
            <summary>
            取消热键定义
            </summary>
            <param name="hWnd">要取消热键的窗口的句柄</param>
            <param name="id">要取消热键的ID</param>
            <returns></returns>
        </member>
        <member name="M:CCWin.Win32.NativeMethods.AnimateWindow(System.IntPtr,System.Int32,System.Int32)">
            <summary>
            执行动画
            </summary>
            <param name="whnd">控件句柄</param>
            <param name="dwtime">动画时间</param>
            <param name="dwflag">动画组合名称</param>
            <returns>bool值，动画是否成功</returns>
        </member>
        <member name="M:CCWin.Win32.NativeMethods.GetPixel(System.IntPtr,System.Int32,System.Int32)">
            <summary>  
            在指定的设备场景中取得一个像素的RGB值  
            </summary>  
            <param name="hdc">一个设备场景的句柄</param>  
            <param name="nXPos">逻辑坐标中要检查的横坐标</param>  
            <param name="nYPos">逻辑坐标中要检查的纵坐标</param>  
            <returns>指定点的颜色</returns>  
        </member>
        <member name="T:CCWin.Properties.Resources">
            <summary>
              一个强类型的资源类，用于查找本地化的字符串等。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.ResourceManager">
            <summary>
              返回此类使用的缓存的 ResourceManager 实例。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Culture">
            <summary>
              使用此强类型资源类，为所有资源查找
              重写当前线程的 CurrentUICulture 属性。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources._1_100">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources._checked">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources._out">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.AboutInfo">
             <summary>
               查找类似 这是一个免费的HTML控件，你可以自由使用。
            如有任何问题或建议请加入QQ群：306485590，项目来至Cskin：http://www.cskin.net,是否现在就访问？
            SkinHtmlEditor
            版本:1.0.0
            快捷键	功能
            ctrl+b	给选中字设置为加粗
            ctrl+c	复制选中内容
            ctrl+x	剪切选中内容
            ctrl+v	粘贴
            ctrl+y	重新执行上次操作
            ctrl+z	撤销上一次操作
            ctrl+i	给选中字设置为斜体
            ctrl+u	给选中字加下划线
            ctrl+a	全部选中
            shift+enter	软回车 的本地化字符串。
             </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.AboutText">
            <summary>
              查找类似 关于 的本地化字符串。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.AlBorder">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.AlDateTimePicker">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.allbtn_down">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.allbtn_highlight">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.AlMonthCalendar01">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.AlMonthCalendar02">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.AlMonthCalendar03">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.AlMonthCalendar04">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.AlMonthCalendarBg">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.AlMonthCalendarBtn">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.arrow">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.AutoLayout">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Away">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.BackColor">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.BackPalace">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Bold">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.brush">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.BtnClose_hight">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Busy">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.cancel">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.close">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.close_down">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.close_normal">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.close_over">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.CloseDownBack">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.CloseMouseBack">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.CloseNormlBack">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.color">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Copy">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.CreateLink">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Cut">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Date">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Delete">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Dont_Disturb">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.ellips">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Error">
            <summary>
              查找类似于 (Icon) 的 System.Drawing.Icon 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.fader">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Find">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.ForeColor">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.frameBorderEffect_mouseDownDraw">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.frameBorderEffect_normalDraw">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Full_close_down">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Full_close_hover">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Full_close_normal">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.hight">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.HScroll_Left">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.HScroll_Middle">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.HScroll_Right">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.HTML">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.HtmlSave">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.icon_close_down">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.icon_close_hover">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.icon_close_normal">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.imoffline">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.imonline">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.ImQQAway">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.ImQQBusy">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.ImQQMute">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.ImQQOnline">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.ImQQQme">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Indent">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Information">
            <summary>
              查找类似于 (Icon) 的 System.Drawing.Icon 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.inputbox">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.inputbox_hover">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.InsertHorizontalRule">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.InsertImage">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.InsertOrderedList">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.InsertTable">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.InsertUnorderedList">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Invisible">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.IPhoneQQ_Head_Big">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Italic">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.JustifyCenter">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.JustifyFull">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.JustifyLeft">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.JustifyRight">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.keyboard">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.large">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.main_bluelight_bkg">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.main_light_bkg">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.main_light_bkg_top123">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.main_tab_background">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.main_tab_check">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.main_tab_highlight">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.main_tabbtn_down">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.main_tabbtn_highlight">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.MainPanel">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.MaxDownBack">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.MaxMouseBack">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.MaxNormlBack">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.middle">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.MiniDownBack">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.MiniMouseBack">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.MiniNormlBack">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.MobilePhoneQQAway">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.MobilePhoneQQBusy">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.MobilePhoneQQOn">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.MobileWapQQAway">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.MobileWapQQOn">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.mute">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.New">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.none">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Norml">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.OffLine">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.ok">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.OnLine">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Open">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.OpenFilter">
            <summary>
              查找类似 支持文件|*.html;*.shtml;*.txt;*.htm|所有文件|*.* 的本地化字符串。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.OpenTitle">
            <summary>
              查找类似 请选择文件 的本地化字符串。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Outdent">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Paste">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.pictureBox1_Image">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Preview">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Print">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.pushed">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.QMe">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.QQForPad_Head_Big">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Question">
            <summary>
              查找类似于 (Icon) 的 System.Drawing.Icon 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.rect">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Redo">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.RemoveFormat">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.RestoreDownBack">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.RestoreMouseBack">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.RestoreNormlBack">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.save">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.SaveFilter">
            <summary>
              查找类似 HTML文件|*.html|SHTML文件|*.shtml|HTM文件|*.htm|文本文件|*.txt 的本地化字符串。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.SaveTitle">
            <summary>
              查找类似 保存文件 的本地化字符串。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.separator">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.ShowHTML">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.small">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_away">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_busy">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_imoffline">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_imonline">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_ImQQAway">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_ImQQOnline">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_invisible">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_IPhoneQQ_Head_Small">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_MobilePhoneQQOn">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_MobileWapQQBusy">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_MobileWapQQOn">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_mute">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_Qme">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Small_QQForPad_Head_Small">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.SpellCheck">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.StrikeThrough">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.strInsertTable">
            <summary>
              查找类似 插入表格 的本地化字符串。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Subscript">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Superscript">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.tab_dots_down">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.tab_dots_mouseover">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.tab_dots_normal">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.text">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.texture">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Time">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.tips_light_bkg">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.unChecked">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Underline">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Undo">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Unlink">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.vista_ScrollHorzArrow">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.vista_ScrollHorzShaft">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.vista_ScrollHorzThumb">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.vista_ScrollVertArrow">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.vista_ScrollVertArrow1">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.vista_ScrollVertShaft">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.vista_ScrollVertThumb">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.vista_ScrollVertThumb1">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.VScroll_Down">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.VScroll_Middle">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.VScroll_Middle_Enter">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.VScroll_Up">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Warning">
            <summary>
              查找类似于 (Icon) 的 System.Drawing.Icon 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.WebQQAway">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.WebQQBusy">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.WebQQMute">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.WebQQOnline">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.WebQQQme">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.Wordclean">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="P:CCWin.Properties.Resources.wordcount">
            <summary>
              查找 System.Drawing.Bitmap 类型的本地化资源。
            </summary>
        </member>
        <member name="T:CCWin.Win32.WinAPI.NCHITTEST">
            <summary>
            Location of cursor hot spot returnet in WM_NCHITTEST.
            </summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTERROR">
            <summary>
            On the screen background or on a dividing line between windows 
            (same as HTNOWHERE, except that the DefWindowProc function produces a system beep to indicate an error).
            </summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTTRANSPARENT">
            <summary>
            In a window currently covered by another window in the same thread 
            (the message will be sent to underlying windows in the same thread until one of them returns a code that is not HTTRANSPARENT).
            </summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTNOWHERE">
            <summary>
            On the screen background or on a dividing line between windows.
            </summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTCLIENT">
            <summary>In a client area.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTCAPTION">
            <summary>In a title bar.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTSYSMENU">
            <summary>In a window menu or in a Close button in a child window.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTGROWBOX">
            <summary>In a size box (same as HTSIZE).</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTMENU">
            <summary>In a menu.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTHSCROLL">
            <summary>In a horizontal scroll bar.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTVSCROLL">
            <summary>In the vertical scroll bar.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTMINBUTTON">
            <summary>In a Minimize button.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTMAXBUTTON">
            <summary>In a Maximize button.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTLEFT">
            <summary>In the left border of a resizable window 
            (the user can click the mouse to resize the window horizontally).</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTRIGHT">
            <summary>
            In the right border of a resizable window 
            (the user can click the mouse to resize the window horizontally).
            </summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTTOP">
            <summary>In the upper-horizontal border of a window.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTTOPLEFT">
            <summary>In the upper-left corner of a window border.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTTOPRIGHT">
            <summary>In the upper-right corner of a window border.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTBOTTOM">
            <summary>	In the lower-horizontal border of a resizable window 
            (the user can click the mouse to resize the window vertically).</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTBOTTOMLEFT">
            <summary>In the lower-left corner of a border of a resizable window 
            (the user can click the mouse to resize the window diagonally).</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTBOTTOMRIGHT">
            <summary>	In the lower-right corner of a border of a resizable window 
            (the user can click the mouse to resize the window diagonally).</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTBORDER">
            <summary>In the border of a window that does not have a sizing border.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTCLOSE">
            <summary>In a Close button.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCHITTEST.HTHELP">
            <summary>In a Help button.</summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCCALCSIZE_PARAMS.rectNewForm">
            <summary>
            Contains the new coordinates of a window that has been moved or resized, that is, it is the proposed new window coordinates.
            </summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCCALCSIZE_PARAMS.rectOldForm">
            <summary>
            Contains the coordinates of the window before it was moved or resized.
            </summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCCALCSIZE_PARAMS.rectOldClient">
            <summary>
            Contains the coordinates of the window's client area before the window was moved or resized.
            </summary>
        </member>
        <member name="F:CCWin.Win32.WinAPI.NCCALCSIZE_PARAMS.lpPos">
            <summary>
            Pointer to a WINDOWPOS structure that contains the size and position values specified in the operation that moved or resized the window.
            </summary>
        </member>
    </members>
</doc>
