<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NAudio.WinMM</name>
    </assembly>
    <members>
        <member name="T:NAudio.Wave.Compression.AcmDriver">
            <summary>
            Represents an installed ACM Driver
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.IsCodecInstalled(System.String)">
            <summary>
            Helper function to determine whether a particular codec is installed
            </summary>
            <param name="shortName">The short name of the function</param>
            <returns>Whether the codec is installed</returns>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.AddLocalDriver(System.String)">
            <summary>
            Attempts to add a new ACM driver from a file
            </summary>
            <param name="driverFile">Full path of the .acm or dll file containing the driver</param>
            <returns>Handle to the driver</returns>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.RemoveLocalDriver(NAudio.Wave.Compression.AcmDriver)">
            <summary>
            Removes a driver previously added using AddLocalDriver
            </summary>
            <param name="localDriver">Local driver to remove</param>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.ShowFormatChooseDialog(System.IntPtr,System.String,NAudio.Wave.Compression.AcmFormatEnumFlags,NAudio.Wave.WaveFormat,NAudio.Wave.WaveFormat@,System.String@,System.String@)">
            <summary>
            Show Format Choose Dialog
            </summary>
            <param name="ownerWindowHandle">Owner window handle, can be null</param>
            <param name="windowTitle">Window title</param>
            <param name="enumFlags">Enumeration flags. None to get everything</param>
            <param name="enumFormat">Enumeration format. Only needed with certain enumeration flags</param>
            <param name="selectedFormat">The selected format</param>
            <param name="selectedFormatDescription">Textual description of the selected format</param>
            <param name="selectedFormatTagDescription">Textual description of the selected format tag</param>
            <returns>True if a format was selected</returns>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmDriver.MaxFormatSize">
            <summary>
            Gets the maximum size needed to store a WaveFormat for ACM interop functions
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.FindByShortName(System.String)">
            <summary>
            Finds a Driver by its short name
            </summary>
            <param name="shortName">Short Name</param>
            <returns>The driver, or null if not found</returns>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.EnumerateAcmDrivers">
            <summary>
            Gets a list of the ACM Drivers installed
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.DriverEnumCallback(System.IntPtr,System.IntPtr,NAudio.Wave.Compression.AcmDriverDetailsSupportFlags)">
            <summary>
            The callback for acmDriverEnum
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.#ctor(System.IntPtr)">
            <summary>
            Creates a new ACM Driver object
            </summary>
            <param name="hAcmDriver">Driver handle</param>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmDriver.ShortName">
            <summary>
            The short name of this driver
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmDriver.LongName">
            <summary>
            The full name of this driver
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmDriver.DriverId">
            <summary>
            The driver ID
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.ToString">
            <summary>
            ToString
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmDriver.FormatTags">
            <summary>
            The list of FormatTags for this ACM Driver
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.GetFormats(NAudio.Wave.Compression.AcmFormatTag)">
            <summary>
            Gets all the supported formats for a given format tag
            </summary>
            <param name="formatTag">Format tag</param>
            <returns>Supported formats</returns>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.Open">
            <summary>
            Opens this driver
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.Close">
            <summary>
            Closes this driver
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmDriver.Dispose">
            <summary>
            Dispose
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.AcmDriverAddFlags">
            <summary>
            Flags for use with acmDriverAdd
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverAddFlags.Local">
            <summary>
            ACM_DRIVERADDF_LOCAL
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverAddFlags.Global">
            <summary>
            ACM_DRIVERADDF_GLOBAL
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverAddFlags.Function">
            <summary>
            ACM_DRIVERADDF_FUNCTION
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverAddFlags.NotifyWindowHandle">
            <summary>
            ACM_DRIVERADDF_NOTIFYHWND
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.AcmDriverDetails">
            <summary>
            Interop structure for ACM driver details (ACMDRIVERDETAILS)
            http://msdn.microsoft.com/en-us/library/dd742889%28VS.85%29.aspx
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.structureSize">
            <summary>
            DWORD cbStruct
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.fccType">
            <summary>
            FOURCC fccType
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.fccComp">
            <summary>
            FOURCC fccComp
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.manufacturerId">
            <summary>
            WORD   wMid; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.productId">
            <summary>
            WORD wPid
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.acmVersion">
            <summary>
            DWORD vdwACM
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.driverVersion">
            <summary>
            DWORD vdwDriver
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.supportFlags">
            <summary>
            DWORD  fdwSupport;
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.formatTagsCount">
            <summary>
            DWORD cFormatTags
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.filterTagsCount">
            <summary>
            DWORD cFilterTags
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.hicon">
            <summary>
            HICON hicon
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.shortName">
            <summary>
            TCHAR  szShortName[ACMDRIVERDETAILS_SHORTNAME_CHARS]; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.longName">
            <summary>
            TCHAR  szLongName[ACMDRIVERDETAILS_LONGNAME_CHARS];
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.copyright">
            <summary>
            TCHAR  szCopyright[ACMDRIVERDETAILS_COPYRIGHT_CHARS]; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.licensing">
            <summary>
            TCHAR  szLicensing[ACMDRIVERDETAILS_LICENSING_CHARS]; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.features">
            <summary>
            TCHAR  szFeatures[ACMDRIVERDETAILS_FEATURES_CHARS];
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.ShortNameChars">
            <summary>
            ACMDRIVERDETAILS_SHORTNAME_CHARS
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.LongNameChars">
            <summary>
            ACMDRIVERDETAILS_LONGNAME_CHARS
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.CopyrightChars">
            <summary>
            ACMDRIVERDETAILS_COPYRIGHT_CHARS
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.LicensingChars">
            <summary>
            ACMDRIVERDETAILS_LICENSING_CHARS 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetails.FeaturesChars">
            <summary>
            ACMDRIVERDETAILS_FEATURES_CHARS
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.AcmDriverDetailsSupportFlags">
            <summary>
            Flags indicating what support a particular ACM driver has
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetailsSupportFlags.Codec">
            <summary>ACMDRIVERDETAILS_SUPPORTF_CODEC - Codec</summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetailsSupportFlags.Converter">
            <summary>ACMDRIVERDETAILS_SUPPORTF_CONVERTER - Converter</summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetailsSupportFlags.Filter">
            <summary>ACMDRIVERDETAILS_SUPPORTF_FILTER - Filter</summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetailsSupportFlags.Hardware">
            <summary>ACMDRIVERDETAILS_SUPPORTF_HARDWARE - Hardware</summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetailsSupportFlags.Async">
            <summary>ACMDRIVERDETAILS_SUPPORTF_ASYNC - Async</summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetailsSupportFlags.Local">
            <summary>ACMDRIVERDETAILS_SUPPORTF_LOCAL - Local</summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverDetailsSupportFlags.Disabled">
            <summary>ACMDRIVERDETAILS_SUPPORTF_DISABLED - Disabled</summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverEnumFlags.NoLocal">
            <summary>
            ACM_DRIVERENUMF_NOLOCAL, Only global drivers should be included in the enumeration
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmDriverEnumFlags.Disabled">
            <summary>
            ACM_DRIVERENUMF_DISABLED, Disabled ACM drivers should be included in the enumeration
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.AcmFormat">
            <summary>
            ACM Format
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmFormat.FormatIndex">
            <summary>
            Format Index
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmFormat.FormatTag">
            <summary>
            Format Tag
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmFormat.SupportFlags">
            <summary>
            Support Flags
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmFormat.WaveFormat">
            <summary>
            WaveFormat
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmFormat.WaveFormatByteSize">
            <summary>
            WaveFormat Size
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmFormat.FormatDescription">
            <summary>
            Format Description
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.AcmFormatChoose">
            <summary>
            ACMFORMATCHOOSE
            http://msdn.microsoft.com/en-us/library/dd742911%28VS.85%29.aspx
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.structureSize">
            <summary>
            DWORD cbStruct; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.styleFlags">
            <summary>
            DWORD fdwStyle; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.ownerWindowHandle">
            <summary>
            HWND hwndOwner; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.selectedWaveFormatPointer">
            <summary>
            LPWAVEFORMATEX pwfx; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.selectedWaveFormatByteSize">
            <summary>
            DWORD cbwfx; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.title">
            <summary>
            LPCTSTR pszTitle; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.formatTagDescription">
            <summary>
            TCHAR szFormatTag[ACMFORMATTAGDETAILS_FORMATTAG_CHARS]; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.formatDescription">
            <summary>
            TCHAR szFormat[ACMFORMATDETAILS_FORMAT_CHARS]; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.name">
            <summary>
            LPTSTR pszName; 
            n.b. can be written into
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.nameByteSize">
            <summary>
            DWORD cchName
            Should be at least 128 unless name is zero
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.formatEnumFlags">
            <summary>
            DWORD fdwEnum; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.waveFormatEnumPointer">
            <summary>
            LPWAVEFORMATEX pwfxEnum; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.instanceHandle">
            <summary>
            HINSTANCE hInstance; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.templateName">
            <summary>
            LPCTSTR pszTemplateName; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.customData">
            <summary>
            LPARAM lCustData; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChoose.windowCallbackFunction">
            <summary>
            ACMFORMATCHOOSEHOOKPROC pfnHook; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChooseStyleFlags.None">
            <summary>
            None
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChooseStyleFlags.ShowHelp">
            <summary>
            ACMFORMATCHOOSE_STYLEF_SHOWHELP
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChooseStyleFlags.EnableHook">
            <summary>
            ACMFORMATCHOOSE_STYLEF_ENABLEHOOK
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChooseStyleFlags.EnableTemplate">
            <summary>
            ACMFORMATCHOOSE_STYLEF_ENABLETEMPLATE
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChooseStyleFlags.EnableTemplateHandle">
            <summary>
            ACMFORMATCHOOSE_STYLEF_ENABLETEMPLATEHANDLE
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChooseStyleFlags.InitToWfxStruct">
            <summary>
            ACMFORMATCHOOSE_STYLEF_INITTOWFXSTRUCT
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatChooseStyleFlags.ContextHelp">
            <summary>
            ACMFORMATCHOOSE_STYLEF_CONTEXTHELP
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.AcmFormatDetails">
            <summary>
            ACMFORMATDETAILS
            http://msdn.microsoft.com/en-us/library/dd742913%28VS.85%29.aspx
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatDetails.structSize">
            <summary>
            DWORD cbStruct; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatDetails.formatIndex">
            <summary>
            DWORD dwFormatIndex; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatDetails.formatTag">
            <summary>
            DWORD dwFormatTag; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatDetails.supportFlags">
            <summary>
            DWORD fdwSupport; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatDetails.waveFormatPointer">
            <summary>
            LPWAVEFORMATEX pwfx; 
            </summary>    
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatDetails.waveFormatByteSize">
            <summary>
            DWORD cbwfx; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatDetails.formatDescription">
            <summary>
            TCHAR szFormat[ACMFORMATDETAILS_FORMAT_CHARS];
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatDetails.FormatDescriptionChars">
            <summary>
            ACMFORMATDETAILS_FORMAT_CHARS
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.AcmFormatEnumFlags">
            <summary>
            Format Enumeration Flags
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatEnumFlags.None">
            <summary>
            None
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatEnumFlags.Convert">
            <summary>
            ACM_FORMATENUMF_CONVERT
            The WAVEFORMATEX structure pointed to by the pwfx member of the ACMFORMATDETAILS structure is valid. The enumerator will only enumerate destination formats that can be converted from the given pwfx format. 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatEnumFlags.Hardware">
            <summary>
            ACM_FORMATENUMF_HARDWARE
            The enumerator should only enumerate formats that are supported as native input or output formats on one or more of the installed waveform-audio devices. This flag provides a way for an application to choose only formats native to an installed waveform-audio device. This flag must be used with one or both of the ACM_FORMATENUMF_INPUT and ACM_FORMATENUMF_OUTPUT flags. Specifying both ACM_FORMATENUMF_INPUT and ACM_FORMATENUMF_OUTPUT will enumerate only formats that can be opened for input or output. This is true regardless of whether this flag is specified. 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatEnumFlags.Input">
            <summary>
            ACM_FORMATENUMF_INPUT
            Enumerator should enumerate only formats that are supported for input (recording). 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatEnumFlags.Channels">
            <summary>
            ACM_FORMATENUMF_NCHANNELS 
            The nChannels member of the WAVEFORMATEX structure pointed to by the pwfx member of the ACMFORMATDETAILS structure is valid. The enumerator will enumerate only a format that conforms to this attribute. 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatEnumFlags.SamplesPerSecond">
            <summary>
            ACM_FORMATENUMF_NSAMPLESPERSEC
            The nSamplesPerSec member of the WAVEFORMATEX structure pointed to by the pwfx member of the ACMFORMATDETAILS structure is valid. The enumerator will enumerate only a format that conforms to this attribute. 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatEnumFlags.Output">
            <summary>
            ACM_FORMATENUMF_OUTPUT 
            Enumerator should enumerate only formats that are supported for output (playback). 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatEnumFlags.Suggest">
            <summary>
            ACM_FORMATENUMF_SUGGEST
            The WAVEFORMATEX structure pointed to by the pwfx member of the ACMFORMATDETAILS structure is valid. The enumerator will enumerate all suggested destination formats for the given pwfx format. This mechanism can be used instead of the acmFormatSuggest function to allow an application to choose the best suggested format for conversion. The dwFormatIndex member will always be set to zero on return. 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatEnumFlags.BitsPerSample">
            <summary>
            ACM_FORMATENUMF_WBITSPERSAMPLE
            The wBitsPerSample member of the WAVEFORMATEX structure pointed to by the pwfx member of the ACMFORMATDETAILS structure is valid. The enumerator will enumerate only a format that conforms to this attribute. 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatEnumFlags.FormatTag">
            <summary>
            ACM_FORMATENUMF_WFORMATTAG
            The wFormatTag member of the WAVEFORMATEX structure pointed to by the pwfx member of the ACMFORMATDETAILS structure is valid. The enumerator will enumerate only a format that conforms to this attribute. The dwFormatTag member of the ACMFORMATDETAILS structure must be equal to the wFormatTag member. 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatSuggestFlags.FormatTag">
            <summary>
            ACM_FORMATSUGGESTF_WFORMATTAG
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatSuggestFlags.Channels">
            <summary>
            ACM_FORMATSUGGESTF_NCHANNELS
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatSuggestFlags.SamplesPerSecond">
            <summary>
            ACM_FORMATSUGGESTF_NSAMPLESPERSEC
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatSuggestFlags.BitsPerSample">
            <summary>
            ACM_FORMATSUGGESTF_WBITSPERSAMPLE
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatSuggestFlags.TypeMask">
            <summary>
            ACM_FORMATSUGGESTF_TYPEMASK
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.AcmFormatTag">
            <summary>
            ACM Format Tag
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmFormatTag.FormatTagIndex">
            <summary>
            Format Tag Index
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmFormatTag.FormatTag">
            <summary>
            Format Tag
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmFormatTag.FormatSize">
            <summary>
            Format Size
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmFormatTag.SupportFlags">
            <summary>
            Support Flags
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmFormatTag.StandardFormatsCount">
            <summary>
            Standard Formats Count
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmFormatTag.FormatDescription">
            <summary>
            Format Description
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatTagDetails.structureSize">
            <summary>
            DWORD cbStruct; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatTagDetails.formatTagIndex">
            <summary>
            DWORD dwFormatTagIndex; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatTagDetails.formatTag">
            <summary>
            DWORD dwFormatTag; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatTagDetails.formatSize">
            <summary>
            DWORD cbFormatSize; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatTagDetails.supportFlags">
            <summary>
            DWORD fdwSupport;
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatTagDetails.standardFormatsCount">
            <summary>
            DWORD cStandardFormats; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatTagDetails.formatDescription">
            <summary>
            TCHAR szFormatTag[ACMFORMATTAGDETAILS_FORMATTAG_CHARS]; 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmFormatTagDetails.FormatTagDescriptionChars">
            <summary>
            ACMFORMATTAGDETAILS_FORMATTAG_CHARS
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.AcmInterop">
            <summary>
            Interop definitions for Windows ACM (Audio Compression Manager) API
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.AcmInterop.AcmFormatChooseHookProc">
            <summary>
            http://msdn.microsoft.com/en-us/library/dd742910%28VS.85%29.aspx
            UINT ACMFORMATCHOOSEHOOKPROC acmFormatChooseHookProc(
              HWND hwnd,     
              UINT uMsg,     
              WPARAM wParam, 
              LPARAM lParam  
            </summary>        
        </member>
        <member name="M:NAudio.Wave.Compression.AcmInterop.acmStreamOpen2(System.IntPtr@,System.IntPtr,System.IntPtr,System.IntPtr,NAudio.Wave.Compression.WaveFilter,System.IntPtr,System.IntPtr,NAudio.Wave.Compression.AcmStreamOpenFlags)">
            <summary>
            A version with pointers for troubleshooting
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.AcmStream">
            <summary>
            AcmStream encapsulates an Audio Compression Manager Stream
            used to convert audio from one format to another
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmStream.#ctor(NAudio.Wave.WaveFormat,NAudio.Wave.WaveFormat)">
            <summary>
            Creates a new ACM stream to convert one format to another. Note that
            not all conversions can be done in one step
            </summary>
            <param name="sourceFormat">The source audio format</param>
            <param name="destFormat">The destination audio format</param>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmStream.#ctor(System.IntPtr,NAudio.Wave.WaveFormat,NAudio.Wave.Compression.WaveFilter)">
            <summary>
            Creates a new ACM stream to convert one format to another, using a 
            specified driver identifier and wave filter
            </summary>
            <param name="driverId">the driver identifier</param>
            <param name="sourceFormat">the source format</param>
            <param name="waveFilter">the wave filter</param>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmStream.SourceToDest(System.Int32)">
            <summary>
            Returns the number of output bytes for a given number of input bytes
            </summary>
            <param name="source">Number of input bytes</param>
            <returns>Number of output bytes</returns>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmStream.DestToSource(System.Int32)">
            <summary>
            Returns the number of source bytes for a given number of destination bytes
            </summary>
            <param name="dest">Number of destination bytes</param>
            <returns>Number of source bytes</returns>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmStream.SuggestPcmFormat(NAudio.Wave.WaveFormat)">
            <summary>
            Suggests an appropriate PCM format that the compressed format can be converted
            to in one step
            </summary>
            <param name="compressedFormat">The compressed format</param>
            <returns>The PCM format</returns>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmStream.SourceBuffer">
            <summary>
            Returns the Source Buffer. Fill this with data prior to calling convert
            </summary>
        </member>
        <member name="P:NAudio.Wave.Compression.AcmStream.DestBuffer">
            <summary>
            Returns the Destination buffer. This will contain the converted data
            after a successful call to Convert
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmStream.Reposition">
            <summary>
            Report that we have repositioned in the source stream
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmStream.Convert(System.Int32,System.Int32@)">
            <summary>
            Converts the contents of the SourceBuffer into the DestinationBuffer
            </summary>
            <param name="bytesToConvert">The number of bytes in the SourceBuffer
            that need to be converted</param>
            <param name="sourceBytesConverted">The number of source bytes actually converted</param>
            <returns>The number of converted bytes in the DestinationBuffer</returns>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmStream.Convert(System.Int32)">
            <summary>
            Converts the contents of the SourceBuffer into the DestinationBuffer
            </summary>
            <param name="bytesToConvert">The number of bytes in the SourceBuffer
            that need to be converted</param>
            <returns>The number of converted bytes in the DestinationBuffer</returns>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmStream.Dispose">
            <summary>
            Frees resources associated with this ACM Stream
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmStream.Dispose(System.Boolean)">
            <summary>
            Frees resources associated with this ACM Stream
            </summary>
        </member>
        <member name="M:NAudio.Wave.Compression.AcmStream.Finalize">
            <summary>
            Frees resources associated with this ACM Stream
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamHeaderStatusFlags.Done">
            <summary>
            ACMSTREAMHEADER_STATUSF_DONE
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamHeaderStatusFlags.Prepared">
            <summary>
            ACMSTREAMHEADER_STATUSF_PREPARED
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamHeaderStatusFlags.InQueue">
            <summary>
            ACMSTREAMHEADER_STATUSF_INQUEUE
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.AcmStreamHeaderStruct">
            <summary>
            Interop structure for ACM stream headers.
            ACMSTREAMHEADER 
            http://msdn.microsoft.com/en-us/library/dd742926%28VS.85%29.aspx
            </summary>    
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamOpenFlags.Query">
            <summary>
            ACM_STREAMOPENF_QUERY, ACM will be queried to determine whether it supports the given conversion. A conversion stream will not be opened, and no handle will be returned in the phas parameter. 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamOpenFlags.Async">
            <summary>
            ACM_STREAMOPENF_ASYNC, Stream conversion should be performed asynchronously. If this flag is specified, the application can use a callback function to be notified when the conversion stream is opened and closed and after each buffer is converted. In addition to using a callback function, an application can examine the fdwStatus member of the ACMSTREAMHEADER structure for the ACMSTREAMHEADER_STATUSF_DONE flag. 
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamOpenFlags.NonRealTime">
            <summary>
            ACM_STREAMOPENF_NONREALTIME, ACM will not consider time constraints when converting the data. By default, the driver will attempt to convert the data in real time. For some formats, specifying this flag might improve the audio quality or other characteristics.
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamOpenFlags.CallbackTypeMask">
            <summary>
            CALLBACK_TYPEMASK, callback type mask
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamOpenFlags.CallbackNull">
            <summary>
            CALLBACK_NULL, no callback
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamOpenFlags.CallbackWindow">
            <summary>
            CALLBACK_WINDOW, dwCallback is a HWND
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamOpenFlags.CallbackTask">
            <summary>
            CALLBACK_TASK, dwCallback is a HTASK
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamOpenFlags.CallbackFunction">
            <summary>
            CALLBACK_FUNCTION, dwCallback is a FARPROC
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamOpenFlags.CallbackThread">
            <summary>
            CALLBACK_THREAD, thread ID replaces 16 bit task
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamOpenFlags.CallbackEvent">
            <summary>
            CALLBACK_EVENT, dwCallback is an EVENT Handle
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamSizeFlags.Source">
            <summary>
            ACM_STREAMSIZEF_SOURCE
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.AcmStreamSizeFlags.Destination">
            <summary>
            ACM_STREAMSIZEF_DESTINATION
            </summary>
        </member>
        <member name="T:NAudio.Wave.Compression.WaveFilter">
            <summary>
            Summary description for WaveFilter.
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.WaveFilter.StructureSize">
            <summary>
            cbStruct
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.WaveFilter.FilterTag">
            <summary>
            dwFilterTag
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.WaveFilter.Filter">
            <summary>
            fdwFilter
            </summary>
        </member>
        <member name="F:NAudio.Wave.Compression.WaveFilter.Reserved">
            <summary>
            reserved
            </summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.CountDrivers">
            <summary>ACM_METRIC_COUNT_DRIVERS</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.CountCodecs">
            <summary>ACM_METRIC_COUNT_CODECS</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.CountConverters">
            <summary>ACM_METRIC_COUNT_CONVERTERS</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.CountFilters">
            <summary>ACM_METRIC_COUNT_FILTERS</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.CountDisabled">
            <summary>ACM_METRIC_COUNT_DISABLED</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.CountHardware">
            <summary>ACM_METRIC_COUNT_HARDWARE</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.CountLocalDrivers">
            <summary>ACM_METRIC_COUNT_LOCAL_DRIVERS</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.CountLocalCodecs">
            <summary>ACM_METRIC_COUNT_LOCAL_CODECS</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.CountLocalConverters">
            <summary>ACM_METRIC_COUNT_LOCAL_CONVERTERS</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.CountLocalFilters">
            <summary>ACM_METRIC_COUNT_LOCAL_FILTERS</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.CountLocalDisabled">
            <summary>ACM_METRIC_COUNT_LOCAL_DISABLED</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.HardwareWaveInput">
            <summary>ACM_METRIC_HARDWARE_WAVE_INPUT</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.HardwareWaveOutput">
            <summary>ACM_METRIC_HARDWARE_WAVE_OUTPUT</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.MaxSizeFormat">
            <summary>ACM_METRIC_MAX_SIZE_FORMAT</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.MaxSizeFilter">
            <summary>ACM_METRIC_MAX_SIZE_FILTER</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.DriverSupport">
            <summary>ACM_METRIC_DRIVER_SUPPORT</summary>
        </member>
        <member name="F:NAudio.Wave.AcmMetrics.DriverPriority">
            <summary>ACM_METRIC_DRIVER_PRIORITY</summary>
        </member>
        <member name="F:NAudio.Wave.AcmStreamConvertFlags.BlockAlign">
            <summary>
            ACM_STREAMCONVERTF_BLOCKALIGN
            </summary>
        </member>
        <member name="F:NAudio.Wave.AcmStreamConvertFlags.Start">
            <summary>
            ACM_STREAMCONVERTF_START
            </summary>
        </member>
        <member name="F:NAudio.Wave.AcmStreamConvertFlags.End">
            <summary>
            ACM_STREAMCONVERTF_END
            </summary>
        </member>
        <member name="T:NAudio.Wave.MmTime">
            <summary>
            MmTime
            http://msdn.microsoft.com/en-us/library/dd757347(v=VS.85).aspx
            </summary>
        </member>
        <member name="T:NAudio.Wave.WaveCallbackStrategy">
            <summary>
            Wave Callback Strategy
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveCallbackStrategy.FunctionCallback">
            <summary>
            Use a function
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveCallbackStrategy.NewWindow">
            <summary>
            Create a new window (should only be done if on GUI thread)
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveCallbackStrategy.ExistingWindow">
            <summary>
            Use an existing window handle
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveCallbackStrategy.Event">
            <summary>
            Use an event handle
            </summary>
        </member>
        <member name="T:NAudio.Wave.WaveHeader">
            <summary>
            WaveHeader interop structure (WAVEHDR)
            http://msdn.microsoft.com/en-us/library/dd743837%28VS.85%29.aspx
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeader.dataBuffer">
            <summary>pointer to locked data buffer (lpData)</summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeader.bufferLength">
            <summary>length of data buffer (dwBufferLength)</summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeader.bytesRecorded">
            <summary>used for input only (dwBytesRecorded)</summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeader.userData">
            <summary>for client's use (dwUser)</summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeader.flags">
            <summary>assorted flags (dwFlags)</summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeader.loops">
            <summary>loop control counter (dwLoops)</summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeader.next">
            <summary>PWaveHdr, reserved for driver (lpNext)</summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeader.reserved">
            <summary>reserved for driver</summary>
        </member>
        <member name="T:NAudio.Wave.WaveHeaderFlags">
            <summary>
            Wave Header Flags enumeration
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeaderFlags.BeginLoop">
            <summary>
            WHDR_BEGINLOOP
            This buffer is the first buffer in a loop.  This flag is used only with output buffers.
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeaderFlags.Done">
            <summary>
            WHDR_DONE
            Set by the device driver to indicate that it is finished with the buffer and is returning it to the application.
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeaderFlags.EndLoop">
            <summary>
            WHDR_ENDLOOP
            This buffer is the last buffer in a loop.  This flag is used only with output buffers.
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeaderFlags.InQueue">
            <summary>
            WHDR_INQUEUE
            Set by Windows to indicate that the buffer is queued for playback.
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveHeaderFlags.Prepared">
            <summary>
            WHDR_PREPARED
            Set by Windows to indicate that the buffer has been prepared with the waveInPrepareHeader or waveOutPrepareHeader function.
            </summary>
        </member>
        <member name="T:NAudio.Wave.WaveInCapabilities">
            <summary>
            WaveInCapabilities structure (based on WAVEINCAPS2 from mmsystem.h)
            http://msdn.microsoft.com/en-us/library/ms713726(VS.85).aspx
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInCapabilities.manufacturerId">
            <summary>
            wMid
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInCapabilities.productId">
            <summary>
            wPid
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInCapabilities.driverVersion">
            <summary>
            vDriverVersion
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInCapabilities.productName">
            <summary>
            Product Name (szPname)
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInCapabilities.supportedFormats">
            <summary>
            Supported formats (bit flags) dwFormats 
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInCapabilities.channels">
            <summary>
            Supported channels (1 for mono 2 for stereo) (wChannels)
            Seems to be set to -1 on a lot of devices
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInCapabilities.reserved">
            <summary>
            wReserved1
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInCapabilities.Channels">
            <summary>
            Number of channels supported
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInCapabilities.ProductName">
            <summary>
            The product name
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInCapabilities.NameGuid">
            <summary>
            The device name Guid (if provided)
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInCapabilities.ProductGuid">
            <summary>
            The product name Guid (if provided)
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInCapabilities.ManufacturerGuid">
            <summary>
            The manufacturer guid (if provided)
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInCapabilities.SupportsWaveFormat(NAudio.Wave.SupportedWaveFormat)">
            <summary>
            Checks to see if a given SupportedWaveFormat is supported
            </summary>
            <param name="waveFormat">The SupportedWaveFormat</param>
            <returns>true if supported</returns>
        </member>
        <member name="M:NAudio.Wave.WaveCapabilitiesHelpers.GetNameFromGuid(System.Guid)">
            <summary>
            The device name from the registry if supported
            </summary>
        </member>
        <member name="T:NAudio.Wave.WaveInterop">
            <summary>
            MME Wave function interop
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInterop.WaveInOutOpenFlags.CallbackNull">
            <summary>
            CALLBACK_NULL
            No callback
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInterop.WaveInOutOpenFlags.CallbackFunction">
            <summary>
            CALLBACK_FUNCTION
            dwCallback is a FARPROC 
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInterop.WaveInOutOpenFlags.CallbackEvent">
            <summary>
            CALLBACK_EVENT
            dwCallback is an EVENT handle 
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInterop.WaveInOutOpenFlags.CallbackWindow">
            <summary>
            CALLBACK_WINDOW
            dwCallback is a HWND 
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInterop.WaveInOutOpenFlags.CallbackThread">
            <summary>
            CALLBACK_THREAD
            callback is a thread ID 
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInterop.WaveMessage.WaveInOpen">
            <summary>
            WIM_OPEN
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInterop.WaveMessage.WaveInClose">
            <summary>
            WIM_CLOSE
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInterop.WaveMessage.WaveInData">
            <summary>
            WIM_DATA
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInterop.WaveMessage.WaveOutClose">
            <summary>
            WOM_CLOSE
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInterop.WaveMessage.WaveOutDone">
            <summary>
            WOM_DONE
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveInterop.WaveMessage.WaveOutOpen">
            <summary>
            WOM_OPEN
            </summary>
        </member>
        <member name="T:NAudio.Wave.WaveOutCapabilities">
            <summary>
            WaveOutCapabilities structure (based on WAVEOUTCAPS2 from mmsystem.h)
            http://msdn.microsoft.com/library/default.asp?url=/library/en-us/multimed/htm/_win32_waveoutcaps_str.asp
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutCapabilities.manufacturerId">
            <summary>
            wMid
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutCapabilities.productId">
            <summary>
            wPid
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutCapabilities.driverVersion">
            <summary>
            vDriverVersion
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutCapabilities.productName">
            <summary>
            Product Name (szPname)
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutCapabilities.supportedFormats">
            <summary>
            Supported formats (bit flags) dwFormats 
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutCapabilities.channels">
            <summary>
            Supported channels (1 for mono 2 for stereo) (wChannels)
            Seems to be set to -1 on a lot of devices
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutCapabilities.reserved">
            <summary>
            wReserved1
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutCapabilities.support">
            <summary>
            Optional functionality supported by the device
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveOutCapabilities.Channels">
            <summary>
            Number of channels supported
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveOutCapabilities.SupportsPlaybackRateControl">
            <summary>
            Whether playback control is supported
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveOutCapabilities.ProductName">
            <summary>
            The product name
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutCapabilities.SupportsWaveFormat(NAudio.Wave.SupportedWaveFormat)">
            <summary>
            Checks to see if a given SupportedWaveFormat is supported
            </summary>
            <param name="waveFormat">The SupportedWaveFormat</param>
            <returns>true if supported</returns>
        </member>
        <member name="P:NAudio.Wave.WaveOutCapabilities.NameGuid">
            <summary>
            The device name Guid (if provided)
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveOutCapabilities.ProductGuid">
            <summary>
            The product name Guid (if provided)
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveOutCapabilities.ManufacturerGuid">
            <summary>
            The manufacturer guid (if provided)
            </summary>
        </member>
        <member name="T:NAudio.Wave.SupportedWaveFormat">
            <summary>
            Supported wave formats for WaveOutCapabilities
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_1M08">
            <summary>
            11.025 kHz, Mono,   8-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_1S08">
            <summary>
            11.025 kHz, Stereo, 8-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_1M16">
            <summary>
            11.025 kHz, Mono,   16-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_1S16">
            <summary>
            11.025 kHz, Stereo, 16-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_2M08">
            <summary>
            22.05  kHz, Mono,   8-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_2S08">
            <summary>
            22.05  kHz, Stereo, 8-bit 
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_2M16">
            <summary>
            22.05  kHz, Mono,   16-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_2S16">
            <summary>
            22.05  kHz, Stereo, 16-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_4M08">
            <summary>
            44.1   kHz, Mono,   8-bit 
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_4S08">
            <summary>
            44.1   kHz, Stereo, 8-bit 
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_4M16">
            <summary>
            44.1   kHz, Mono,   16-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_4S16">
            <summary>
             44.1   kHz, Stereo, 16-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_44M08">
            <summary>
            44.1   kHz, Mono,   8-bit 
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_44S08">
            <summary>
            44.1   kHz, Stereo, 8-bit 
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_44M16">
            <summary>
            44.1   kHz, Mono,   16-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_44S16">
            <summary>
            44.1   kHz, Stereo, 16-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_48M08">
            <summary>
            48     kHz, Mono,   8-bit 
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_48S08">
            <summary>
             48     kHz, Stereo, 8-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_48M16">
            <summary>
            48     kHz, Mono,   16-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_48S16">
            <summary>
            48     kHz, Stereo, 16-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_96M08">
            <summary>
            96     kHz, Mono,   8-bit 
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_96S08">
            <summary>
            96     kHz, Stereo, 8-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_96M16">
            <summary>
            96     kHz, Mono,   16-bit
            </summary>
        </member>
        <member name="F:NAudio.Wave.SupportedWaveFormat.WAVE_FORMAT_96S16">
            <summary>
            96     kHz, Stereo, 16-bit
            </summary>
        </member>
        <member name="T:NAudio.Wave.WaveOutSupport">
            <summary>
            Flags indicating what features this WaveOut device supports
            </summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutSupport.Pitch">
            <summary>supports pitch control (WAVECAPS_PITCH)</summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutSupport.PlaybackRate">
            <summary>supports playback rate control (WAVECAPS_PLAYBACKRATE)</summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutSupport.Volume">
            <summary>supports volume control (WAVECAPS_VOLUME)</summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutSupport.LRVolume">
            <summary>supports separate left-right volume control (WAVECAPS_LRVOLUME)</summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutSupport.Sync">
            <summary>(WAVECAPS_SYNC)</summary>
        </member>
        <member name="F:NAudio.Wave.WaveOutSupport.SampleAccurate">
            <summary>(WAVECAPS_SAMPLEACCURATE)</summary>
        </member>
        <member name="T:NAudio.Wave.AcmMp3FrameDecompressor">
            <summary>
            MP3 Frame Decompressor using ACM
            </summary>
        </member>
        <member name="M:NAudio.Wave.AcmMp3FrameDecompressor.#ctor(NAudio.Wave.WaveFormat)">
            <summary>
            Creates a new ACM frame decompressor
            </summary>
            <param name="sourceFormat">The MP3 source format</param>
        </member>
        <member name="P:NAudio.Wave.AcmMp3FrameDecompressor.OutputFormat">
            <summary>
            Output format (PCM)
            </summary>
        </member>
        <member name="M:NAudio.Wave.AcmMp3FrameDecompressor.DecompressFrame(NAudio.Wave.Mp3Frame,System.Byte[],System.Int32)">
            <summary>
            Decompresses a frame
            </summary>
            <param name="frame">The MP3 frame</param>
            <param name="dest">destination buffer</param>
            <param name="destOffset">Offset within destination buffer</param>
            <returns>Bytes written into destination buffer</returns>
        </member>
        <member name="M:NAudio.Wave.AcmMp3FrameDecompressor.Reset">
            <summary>
            Resets the MP3 Frame Decompressor after a reposition operation
            </summary>
        </member>
        <member name="M:NAudio.Wave.AcmMp3FrameDecompressor.Dispose">
            <summary>
            Disposes of this MP3 frame decompressor
            </summary>
        </member>
        <member name="M:NAudio.Wave.AcmMp3FrameDecompressor.Finalize">
            <summary>
            Finalizer ensuring that resources get released properly
            </summary>
        </member>
        <member name="T:NAudio.Wave.WaveFormatConversionProvider">
            <summary>
            IWaveProvider that passes through an ACM Codec
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveFormatConversionProvider.#ctor(NAudio.Wave.WaveFormat,NAudio.Wave.IWaveProvider)">
            <summary>
            Create a new WaveFormat conversion stream
            </summary>
            <param name="targetFormat">Desired output format</param>
            <param name="sourceProvider">Source Provider</param>
        </member>
        <member name="P:NAudio.Wave.WaveFormatConversionProvider.WaveFormat">
            <summary>
            Gets the WaveFormat of this stream
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveFormatConversionProvider.Reposition">
            <summary>
            Indicates that a reposition has taken place, and internal buffers should be reset
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveFormatConversionProvider.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads bytes from this stream
            </summary>
            <param name="buffer">Buffer to read into</param>
            <param name="offset">Offset in buffer to read into</param>
            <param name="count">Number of bytes to read</param>
            <returns>Number of bytes read</returns>
        </member>
        <member name="M:NAudio.Wave.WaveFormatConversionProvider.Dispose(System.Boolean)">
            <summary>
            Disposes this stream
            </summary>
            <param name="disposing">true if the user called this</param>
        </member>
        <member name="M:NAudio.Wave.WaveFormatConversionProvider.Dispose">
            <summary>
            Disposes this resource
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveFormatConversionProvider.Finalize">
            <summary>
            Finalizer
            </summary>
        </member>
        <member name="T:NAudio.Wave.WaveFormatConversionStream">
            <summary>
            WaveStream that passes through an ACM Codec
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveFormatConversionStream.#ctor(NAudio.Wave.WaveFormat,NAudio.Wave.WaveStream)">
            <summary>
            Create a new WaveFormat conversion stream
            </summary>
            <param name="targetFormat">Desired output format</param>
            <param name="sourceStream">Source stream</param>
        </member>
        <member name="M:NAudio.Wave.WaveFormatConversionStream.CreatePcmStream(NAudio.Wave.WaveStream)">
            <summary>
            Creates a stream that can convert to PCM
            </summary>
            <param name="sourceStream">The source stream</param>
            <returns>A PCM stream</returns>
        </member>
        <member name="P:NAudio.Wave.WaveFormatConversionStream.Position">
            <summary>
            Gets or sets the current position in the stream
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveFormatConversionStream.SourceToDest(System.Int32)">
            <summary>
            Converts source bytes to destination bytes
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveFormatConversionStream.DestToSource(System.Int32)">
            <summary>
            Converts destination bytes to source bytes
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveFormatConversionStream.Length">
            <summary>
            Returns the stream length
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveFormatConversionStream.WaveFormat">
            <summary>
            Gets the WaveFormat of this stream
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveFormatConversionStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            
            </summary>
            <param name="buffer">Buffer to read into</param>
            <param name="offset">Offset within buffer to write to</param>
            <param name="count">Number of bytes to read</param>
            <returns>Bytes read</returns>
        </member>
        <member name="M:NAudio.Wave.WaveFormatConversionStream.Dispose(System.Boolean)">
            <summary>
            Disposes this stream
            </summary>
            <param name="disposing">true if the user called this</param>
        </member>
        <member name="T:NAudio.Wave.WaveInBuffer">
            <summary>
            A buffer of Wave samples
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInBuffer.#ctor(System.IntPtr,System.Int32)">
            <summary>
            creates a new wavebuffer
            </summary>
            <param name="waveInHandle">WaveIn device to write to</param>
            <param name="bufferSize">Buffer size in bytes</param>
        </member>
        <member name="M:NAudio.Wave.WaveInBuffer.Reuse">
            <summary>
            Place this buffer back to record more audio
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInBuffer.Finalize">
            <summary>
            Finalizer for this wave buffer
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInBuffer.Dispose">
            <summary>
            Releases resources held by this WaveBuffer
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInBuffer.Dispose(System.Boolean)">
            <summary>
            Releases resources held by this WaveBuffer
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInBuffer.Data">
            <summary>
            Provides access to the actual record buffer (for reading only)
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInBuffer.Done">
            <summary>
            Indicates whether the Done flag is set on this buffer
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInBuffer.InQueue">
            <summary>
            Indicates whether the InQueue flag is set on this buffer
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInBuffer.BytesRecorded">
            <summary>
            Number of bytes recorded
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInBuffer.BufferSize">
            <summary>
            The buffer size in bytes
            </summary>
        </member>
        <member name="T:NAudio.Wave.WaveInEvent">
            <summary>
            Recording using waveIn api with event callbacks.
            Use this for recording in non-gui applications
            Events are raised as recorded buffers are made available
            </summary>
        </member>
        <member name="E:NAudio.Wave.WaveInEvent.DataAvailable">
            <summary>
            Indicates recorded data is available 
            </summary>
        </member>
        <member name="E:NAudio.Wave.WaveInEvent.RecordingStopped">
            <summary>
            Indicates that all recorded data has now been received.
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInEvent.#ctor">
            <summary>
            Prepares a Wave input device for recording
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInEvent.DeviceCount">
            <summary>
            Returns the number of Wave In devices available in the system
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInEvent.GetCapabilities(System.Int32)">
            <summary>
            Retrieves the capabilities of a waveIn device
            </summary>
            <param name="devNumber">Device to test</param>
            <returns>The WaveIn device capabilities</returns>
        </member>
        <member name="P:NAudio.Wave.WaveInEvent.BufferMilliseconds">
            <summary>
            Milliseconds for the buffer. Recommended value is 100ms
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInEvent.NumberOfBuffers">
            <summary>
            Number of Buffers to use (usually 2 or 3)
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveInEvent.DeviceNumber">
            <summary>
            The device number to use
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInEvent.StartRecording">
            <summary>
            Start recording
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInEvent.StopRecording">
            <summary>
            Stop recording
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInEvent.GetPosition">
            <summary>
            Gets the current position in bytes from the wave input device.
            it calls directly into waveInGetPosition)
            </summary>
            <returns>Position in bytes</returns>
        </member>
        <member name="P:NAudio.Wave.WaveInEvent.WaveFormat">
            <summary>
            WaveFormat we are recording in
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInEvent.Dispose(System.Boolean)">
            <summary>
            Dispose pattern
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInEvent.GetMixerLine">
            <summary>
            Microphone Level
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveInEvent.Dispose">
            <summary>
            Dispose method
            </summary>
        </member>
        <member name="T:NAudio.Wave.WaveOutBuffer">
            <summary>
            A buffer of Wave samples for streaming to a Wave Output device
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutBuffer.#ctor(System.IntPtr,System.Int32,NAudio.Wave.IWaveProvider,System.Object)">
            <summary>
            creates a new wavebuffer
            </summary>
            <param name="hWaveOut">WaveOut device to write to</param>
            <param name="bufferSize">Buffer size in bytes</param>
            <param name="bufferFillStream">Stream to provide more data</param>
            <param name="waveOutLock">Lock to protect WaveOut API's from being called on >1 thread</param>
        </member>
        <member name="M:NAudio.Wave.WaveOutBuffer.Finalize">
            <summary>
            Finalizer for this wave buffer
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutBuffer.Dispose">
            <summary>
            Releases resources held by this WaveBuffer
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutBuffer.Dispose(System.Boolean)">
            <summary>
            Releases resources held by this WaveBuffer
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutBuffer.OnDone">
            this is called by the WAVE callback and should be used to refill the buffer
        </member>
        <member name="P:NAudio.Wave.WaveOutBuffer.InQueue">
            <summary>
            Whether the header's in queue flag is set
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveOutBuffer.BufferSize">
            <summary>
            The buffer size in bytes
            </summary>
        </member>
        <member name="T:NAudio.Wave.WaveOutEvent">
            <summary>
            Alternative WaveOut class, making use of the Event callback
            </summary>
        </member>
        <member name="E:NAudio.Wave.WaveOutEvent.PlaybackStopped">
            <summary>
            Indicates playback has stopped automatically
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveOutEvent.DesiredLatency">
            <summary>
            Gets or sets the desired latency in milliseconds
            Should be set before a call to Init
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveOutEvent.NumberOfBuffers">
            <summary>
            Gets or sets the number of buffers used
            Should be set before a call to Init
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveOutEvent.DeviceNumber">
            <summary>
            Gets or sets the device number
            Should be set before a call to Init
            This must be between -1 and <see>DeviceCount</see> - 1.
            -1 means stick to default device even default device is changed
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutEvent.#ctor">
            <summary>
            Opens a WaveOut device
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutEvent.Init(NAudio.Wave.IWaveProvider)">
            <summary>
            Initialises the WaveOut device
            </summary>
            <param name="waveProvider">WaveProvider to play</param>
        </member>
        <member name="M:NAudio.Wave.WaveOutEvent.Play">
            <summary>
            Start playing the audio from the WaveStream
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutEvent.Pause">
            <summary>
            Pause the audio
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutEvent.Resume">
            <summary>
            Resume playing after a pause from the same position
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutEvent.Stop">
            <summary>
            Stop and reset the WaveOut device
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutEvent.GetPosition">
            <summary>
            Gets the current position in bytes from the wave output device.
            (n.b. this is not the same thing as the position within your reader
            stream - it calls directly into waveOutGetPosition)
            </summary>
            <returns>Position in bytes</returns>
        </member>
        <member name="P:NAudio.Wave.WaveOutEvent.OutputWaveFormat">
            <summary>
            Gets a <see cref="T:NAudio.Wave.WaveFormat"/> instance indicating the format the hardware is using.
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveOutEvent.PlaybackState">
            <summary>
            Playback State
            </summary>
        </member>
        <member name="P:NAudio.Wave.WaveOutEvent.Volume">
            <summary>
            Volume for this device 1.0 is full scale
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutEvent.Dispose">
            <summary>
            Closes this WaveOut device
            </summary>
        </member>
        <member name="M:NAudio.Wave.WaveOutEvent.Dispose(System.Boolean)">
            <summary>
            Closes the WaveOut device and disposes of buffers
            </summary>
            <param name="disposing">True if called from <see>Dispose</see></param>
        </member>
        <member name="M:NAudio.Wave.WaveOutEvent.Finalize">
            <summary>
            Finalizer. Only called when user forgets to call <see>Dispose</see>
            </summary>
        </member>
        <member name="T:NAudio.Mixer.BooleanMixerControl">
            <summary>
            Boolean mixer control
            </summary>
        </member>
        <member name="M:NAudio.Mixer.BooleanMixerControl.GetDetails(System.IntPtr)">
            <summary>
            Gets the details for this control
            </summary>
            <param name="pDetails">memory pointer</param>
        </member>
        <member name="P:NAudio.Mixer.BooleanMixerControl.Value">
            <summary>
            The current value of the control
            </summary>
        </member>
        <member name="T:NAudio.Mixer.CustomMixerControl">
            <summary>
            Custom Mixer control
            </summary>
        </member>
        <member name="M:NAudio.Mixer.CustomMixerControl.GetDetails(System.IntPtr)">
            <summary>
            Get the data for this custom control
            </summary>
            <param name="pDetails">pointer to memory to receive data</param>
        </member>
        <member name="T:NAudio.Mixer.ListTextMixerControl">
            <summary>
            List text mixer control
            </summary>
        </member>
        <member name="M:NAudio.Mixer.ListTextMixerControl.GetDetails(System.IntPtr)">
            <summary>
            Get the details for this control
            </summary>
            <param name="pDetails">Memory location to read to</param>
        </member>
        <member name="T:NAudio.Mixer.Mixer">
            <summary>Represents a Windows mixer device</summary>
        </member>
        <member name="P:NAudio.Mixer.Mixer.NumberOfDevices">
            <summary>The number of mixer devices available</summary>	
        </member>
        <member name="M:NAudio.Mixer.Mixer.#ctor(System.Int32)">
            <summary>Connects to the specified mixer</summary>
            <param name="mixerIndex">The index of the mixer to use. 
            This should be between zero and NumberOfDevices - 1</param>
        </member>
        <member name="P:NAudio.Mixer.Mixer.DestinationCount">
            <summary>The number of destinations this mixer supports</summary>
        </member>
        <member name="P:NAudio.Mixer.Mixer.Name">
            <summary>The name of this mixer device</summary>
        </member>
        <member name="P:NAudio.Mixer.Mixer.Manufacturer">
            <summary>The manufacturer code for this mixer device</summary>
        </member>
        <member name="P:NAudio.Mixer.Mixer.ProductID">
            <summary>The product identifier code for this mixer device</summary>
        </member>
        <member name="M:NAudio.Mixer.Mixer.GetDestination(System.Int32)">
            <summary>Retrieve the specified MixerDestination object</summary>
            <param name="destinationIndex">The ID of the destination to use.
            Should be between 0 and DestinationCount - 1</param>
        </member>
        <member name="P:NAudio.Mixer.Mixer.Destinations">
            <summary>
            A way to enumerate the destinations
            </summary>
        </member>
        <member name="P:NAudio.Mixer.Mixer.Mixers">
            <summary>
            A way to enumerate all available devices
            </summary>
        </member>
        <member name="T:NAudio.Mixer.MixerControl">
            <summary>
            Represents a mixer control
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControl.mixerHandle">
            <summary>
            Mixer Handle
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControl.nChannels">
            <summary>
            Number of Channels
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControl.mixerHandleType">
            <summary>
            Mixer Handle Type
            </summary>
        </member>
        <member name="M:NAudio.Mixer.MixerControl.GetMixerControls(System.IntPtr,NAudio.Mixer.MixerLine,NAudio.Mixer.MixerFlags)">
            <summary>
            Gets all the mixer controls
            </summary>
            <param name="mixerHandle">Mixer Handle</param>
            <param name="mixerLine">Mixer Line</param>
            <param name="mixerHandleType">Mixer Handle Type</param>
            <returns></returns>
        </member>
        <member name="M:NAudio.Mixer.MixerControl.GetMixerControl(System.IntPtr,System.Int32,System.Int32,System.Int32,NAudio.Mixer.MixerFlags)">
            <summary>
            Gets a specified Mixer Control
            </summary>
            <param name="mixerHandle">Mixer Handle</param>
            <param name="nLineId">Line ID</param>
            <param name="controlId">Control ID</param>
            <param name="nChannels">Number of Channels</param>
            <param name="mixerFlags">Flags to use (indicates the meaning of mixerHandle)</param>
            <returns></returns>
        </member>
        <member name="M:NAudio.Mixer.MixerControl.GetControlDetails">
            <summary>
            Gets the control details
            </summary>
        </member>
        <member name="M:NAudio.Mixer.MixerControl.GetDetails(System.IntPtr)">
            <summary>
            Gets the control details
            </summary>
            <param name="pDetails"></param>
        </member>
        <member name="P:NAudio.Mixer.MixerControl.Name">
            <summary>
            Mixer control name
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerControl.ControlType">
            <summary>
            Mixer control type
            </summary>
        </member>
        <member name="M:NAudio.Mixer.MixerControl.IsControlBoolean(NAudio.Mixer.MixerControlType)">
            <summary>
            Returns true if this is a boolean control
            </summary>
            <param name="controlType">Control type</param>
        </member>
        <member name="P:NAudio.Mixer.MixerControl.IsBoolean">
            <summary>
            Is this a boolean control
            </summary>
        </member>
        <member name="M:NAudio.Mixer.MixerControl.IsControlListText(NAudio.Mixer.MixerControlType)">
            <summary>
            Determines whether a specified mixer control type is a list text control
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerControl.IsListText">
            <summary>
            True if this is a list text control
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerControl.IsSigned">
            <summary>
            True if this is a signed control
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerControl.IsUnsigned">
            <summary>
            True if this is an unsigned control
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerControl.IsCustom">
            <summary>
            True if this is a custom control
            </summary>
        </member>
        <member name="M:NAudio.Mixer.MixerControl.ToString">
            <summary>
            String representation for debug purposes
            </summary>
        </member>
        <member name="T:NAudio.Mixer.MixerControlType">
            <summary>
            Mixer control types
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Custom">
            <summary>Custom</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.BooleanMeter">
            <summary>Boolean meter</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.SignedMeter">
            <summary>Signed meter</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.PeakMeter">
            <summary>Peak meter</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.UnsignedMeter">
            <summary>Unsigned meter</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Boolean">
            <summary>Boolean</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.OnOff">
            <summary>On Off</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Mute">
            <summary>Mute</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Mono">
            <summary>Mono</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Loudness">
            <summary>Loudness</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.StereoEnhance">
            <summary>Stereo Enhance</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Button">
            <summary>Button</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Decibels">
            <summary>Decibels</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Signed">
            <summary>Signed</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Unsigned">
            <summary>Unsigned</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Percent">
            <summary>Percent</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Slider">
            <summary>Slider</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Pan">
            <summary>Pan</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.QSoundPan">
            <summary>Q-sound pan</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Fader">
            <summary>Fader</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Volume">
            <summary>Volume</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Bass">
            <summary>Bass</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Treble">
            <summary>Treble</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Equalizer">
            <summary>Equaliser</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.SingleSelect">
            <summary>Single Select</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Mux">
            <summary>Mux</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.MultipleSelect">
            <summary>Multiple select</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.Mixer">
            <summary>Mixer</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.MicroTime">
            <summary>Micro time</summary>
        </member>
        <member name="F:NAudio.Mixer.MixerControlType.MilliTime">
            <summary>Milli time</summary>
        </member>
        <member name="T:NAudio.Mixer.MixerFlags">
            <summary>
            Mixer Interop Flags
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.Handle">
            <summary>
            MIXER_OBJECTF_HANDLE 	= 0x80000000;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.Mixer">
            <summary>
            MIXER_OBJECTF_MIXER 	= 0x00000000;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.MixerHandle">
            <summary>
            MIXER_OBJECTF_HMIXER
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.WaveOut">
            <summary>
            MIXER_OBJECTF_WAVEOUT
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.WaveOutHandle">
            <summary>
            MIXER_OBJECTF_HWAVEOUT
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.WaveIn">
            <summary>
            MIXER_OBJECTF_WAVEIN
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.WaveInHandle">
            <summary>
            MIXER_OBJECTF_HWAVEIN
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.MidiOut">
            <summary>
            MIXER_OBJECTF_MIDIOUT
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.MidiOutHandle">
            <summary>
            MIXER_OBJECTF_HMIDIOUT
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.MidiIn">
            <summary>
            MIXER_OBJECTF_MIDIIN
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.MidiInHandle">
            <summary>
            MIXER_OBJECTF_HMIDIIN
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.Aux">
            <summary>
            MIXER_OBJECTF_AUX
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.Value">
            <summary>
            MIXER_GETCONTROLDETAILSF_VALUE      	= 0x00000000;
            MIXER_SETCONTROLDETAILSF_VALUE      	= 0x00000000;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.ListText">
            <summary>
            MIXER_GETCONTROLDETAILSF_LISTTEXT   	= 0x00000001;
            MIXER_SETCONTROLDETAILSF_LISTTEXT   	= 0x00000001;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.QueryMask">
            <summary>
            MIXER_GETCONTROLDETAILSF_QUERYMASK  	= 0x0000000F;
            MIXER_SETCONTROLDETAILSF_QUERYMASK  	= 0x0000000F;
            MIXER_GETLINECONTROLSF_QUERYMASK    	= 0x0000000F;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.All">
            <summary>
            MIXER_GETLINECONTROLSF_ALL          	= 0x00000000;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.OneById">
            <summary>
            MIXER_GETLINECONTROLSF_ONEBYID      	= 0x00000001;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.OneByType">
            <summary>
            MIXER_GETLINECONTROLSF_ONEBYTYPE    	= 0x00000002;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.GetLineInfoOfDestination">
            <summary>
            MIXER_GETLINEINFOF_DESTINATION      	= 0x00000000;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.GetLineInfoOfSource">
            <summary>
            MIXER_GETLINEINFOF_SOURCE           	= 0x00000001;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.GetLineInfoOfLineId">
            <summary>
            MIXER_GETLINEINFOF_LINEID           	= 0x00000002;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.GetLineInfoOfComponentType">
            <summary>
            MIXER_GETLINEINFOF_COMPONENTTYPE    	= 0x00000003;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.GetLineInfoOfTargetType">
            <summary>
            MIXER_GETLINEINFOF_TARGETTYPE       	= 0x00000004;
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerFlags.GetLineInfoOfQueryMask">
            <summary>
            MIXER_GETLINEINFOF_QUERYMASK        	= 0x0000000F;
            </summary>
        </member>
        <member name="T:NAudio.Mixer.MixerInterop.MIXERLINE_LINEF">
            <summary>
            Mixer Line Flags
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.MIXERLINE_LINEF.MIXERLINE_LINEF_ACTIVE">
            <summary>
            Audio line is active. An active line indicates that a signal is probably passing 
            through the line.
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.MIXERLINE_LINEF.MIXERLINE_LINEF_DISCONNECTED">
            <summary>
            Audio line is disconnected. A disconnected line's associated controls can still be 
            modified, but the changes have no effect until the line is connected.
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.MIXERLINE_LINEF.MIXERLINE_LINEF_SOURCE">
            <summary>
            Audio line is an audio source line associated with a single audio destination line. 
            If this flag is not set, this line is an audio destination line associated with zero 
            or more audio source lines.
            </summary>
        </member>
        <member name="T:NAudio.Mixer.MixerInterop.Bounds">
            <summary>
            BOUNDS structure
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.Bounds.minimum">
            <summary>
            dwMinimum / lMinimum / reserved 0
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.Bounds.maximum">
            <summary>
            dwMaximum / lMaximum / reserved 1
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.Bounds.reserved2">
            <summary>
            reserved 2
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.Bounds.reserved3">
            <summary>
            reserved 3
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.Bounds.reserved4">
            <summary>
            reserved 4
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.Bounds.reserved5">
            <summary>
            reserved 5
            </summary>
        </member>
        <member name="T:NAudio.Mixer.MixerInterop.Metrics">
            <summary>
            METRICS structure
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.Metrics.step">
            <summary>
            cSteps / reserved[0]
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.Metrics.customData">
            <summary>
            cbCustomData / reserved[1], number of bytes for control details
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.Metrics.reserved2">
            <summary>
            reserved 2
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.Metrics.reserved3">
            <summary>
            reserved 3
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.Metrics.reserved4">
            <summary>
            reserved 4
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerInterop.Metrics.reserved5">
            <summary>
            reserved 5
            </summary>
        </member>
        <member name="T:NAudio.Mixer.MixerInterop.MIXERCONTROL">
            <summary>
            MIXERCONTROL struct
            http://msdn.microsoft.com/en-us/library/dd757293%28VS.85%29.aspx
            </summary>
        </member>
        <member name="T:NAudio.Mixer.MixerLine">
            <summary>
            Represents a mixer line (source or destination)
            </summary>
        </member>
        <member name="M:NAudio.Mixer.MixerLine.#ctor(System.IntPtr,System.Int32,NAudio.Mixer.MixerFlags)">
            <summary>
            Creates a new mixer destination
            </summary>
            <param name="mixerHandle">Mixer Handle</param>
            <param name="destinationIndex">Destination Index</param>
            <param name="mixerHandleType">Mixer Handle Type</param>
        </member>
        <member name="M:NAudio.Mixer.MixerLine.#ctor(System.IntPtr,System.Int32,System.Int32,NAudio.Mixer.MixerFlags)">
            <summary>
            Creates a new Mixer Source For a Specified Source
            </summary>
            <param name="mixerHandle">Mixer Handle</param>
            <param name="destinationIndex">Destination Index</param>
            <param name="sourceIndex">Source Index</param>
            <param name="mixerHandleType">Flag indicating the meaning of mixerHandle</param>
        </member>
        <member name="M:NAudio.Mixer.MixerLine.GetMixerIdForWaveIn(System.Int32)">
            <summary>
            Creates a new Mixer Source
            </summary>
            <param name="waveInDevice">Wave In Device</param>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.Name">
            <summary>
            Mixer Line Name
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.ShortName">
            <summary>
            Mixer Line short name
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.LineId">
            <summary>
            The line ID
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.ComponentType">
            <summary>
            Component Type
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.TypeDescription">
            <summary>
            Mixer destination type description
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.Channels">
            <summary>
            Number of channels
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.SourceCount">
            <summary>
            Number of sources
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.ControlsCount">
            <summary>
            Number of controls
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.IsActive">
            <summary>
            Is this destination active
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.IsDisconnected">
            <summary>
            Is this destination disconnected
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.IsSource">
            <summary>
            Is this destination a source
            </summary>
        </member>
        <member name="M:NAudio.Mixer.MixerLine.GetSource(System.Int32)">
            <summary>
            Gets the specified source
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.Controls">
            <summary>
            Enumerator for the controls on this Mixer Limne
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.Sources">
            <summary>
            Enumerator for the sources on this Mixer Line
            </summary>
        </member>
        <member name="P:NAudio.Mixer.MixerLine.TargetName">
            <summary>
            The name of the target output device
            </summary>
        </member>
        <member name="M:NAudio.Mixer.MixerLine.ToString">
            <summary>
            Describes this Mixer Line (for diagnostic purposes)
            </summary>
        </member>
        <member name="T:NAudio.Mixer.MixerLineComponentType">
            <summary>
            Mixer Line Component type enumeration
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.DestinationUndefined">
            <summary>
            Audio line is a destination that cannot be defined by one of the standard component types. A mixer device is required to use this component type for line component types that have not been defined by Microsoft Corporation.
            MIXERLINE_COMPONENTTYPE_DST_UNDEFINED
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.DestinationDigital">
            <summary>
            Audio line is a digital destination (for example, digital input to a DAT or CD audio device).
            MIXERLINE_COMPONENTTYPE_DST_DIGITAL 
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.DestinationLine">
            <summary>
            Audio line is a line level destination (for example, line level input from a CD audio device) that will be the final recording source for the analog-to-digital converter (ADC). Because most audio cards for personal computers provide some sort of gain for the recording audio source line, the mixer device will use the MIXERLINE_COMPONENTTYPE_DST_WAVEIN type.
            MIXERLINE_COMPONENTTYPE_DST_LINE
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.DestinationMonitor">
            <summary>
            Audio line is a destination used for a monitor.
            MIXERLINE_COMPONENTTYPE_DST_MONITOR
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.DestinationSpeakers">
            <summary>
            Audio line is an adjustable (gain and/or attenuation) destination intended to drive speakers. This is the typical component type for the audio output of audio cards for personal computers.
            MIXERLINE_COMPONENTTYPE_DST_SPEAKERS
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.DestinationHeadphones">
            <summary>
            Audio line is an adjustable (gain and/or attenuation) destination intended to drive headphones. Most audio cards use the same audio destination line for speakers and headphones, in which case the mixer device simply uses the MIXERLINE_COMPONENTTYPE_DST_SPEAKERS type.
            MIXERLINE_COMPONENTTYPE_DST_HEADPHONES
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.DestinationTelephone">
            <summary>
            Audio line is a destination that will be routed to a telephone line.
            MIXERLINE_COMPONENTTYPE_DST_TELEPHONE
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.DestinationWaveIn">
            <summary>
            Audio line is a destination that will be the final recording source for the waveform-audio input (ADC). This line typically provides some sort of gain or attenuation. This is the typical component type for the recording line of most audio cards for personal computers.
            MIXERLINE_COMPONENTTYPE_DST_WAVEIN
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.DestinationVoiceIn">
            <summary>
            Audio line is a destination that will be the final recording source for voice input. This component type is exactly like MIXERLINE_COMPONENTTYPE_DST_WAVEIN but is intended specifically for settings used during voice recording/recognition. Support for this line is optional for a mixer device. Many mixer devices provide only MIXERLINE_COMPONENTTYPE_DST_WAVEIN.
            MIXERLINE_COMPONENTTYPE_DST_VOICEIN
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.SourceUndefined">
            <summary>
            Audio line is a source that cannot be defined by one of the standard component types. A mixer device is required to use this component type for line component types that have not been defined by Microsoft Corporation.
            MIXERLINE_COMPONENTTYPE_SRC_UNDEFINED
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.SourceDigital">
            <summary>
            Audio line is a digital source (for example, digital output from a DAT or audio CD).
            MIXERLINE_COMPONENTTYPE_SRC_DIGITAL
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.SourceLine">
            <summary>
            Audio line is a line-level source (for example, line-level input from an external stereo) that can be used as an optional recording source. Because most audio cards for personal computers provide some sort of gain for the recording source line, the mixer device will use the MIXERLINE_COMPONENTTYPE_SRC_AUXILIARY type.
            MIXERLINE_COMPONENTTYPE_SRC_LINE
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.SourceMicrophone">
            <summary>
            Audio line is a microphone recording source. Most audio cards for personal computers provide at least two types of recording sources: an auxiliary audio line and microphone input. A microphone audio line typically provides some sort of gain. Audio cards that use a single input for use with a microphone or auxiliary audio line should use the MIXERLINE_COMPONENTTYPE_SRC_MICROPHONE component type.
            MIXERLINE_COMPONENTTYPE_SRC_MICROPHONE
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.SourceSynthesizer">
            <summary>
            Audio line is a source originating from the output of an internal synthesizer. Most audio cards for personal computers provide some sort of MIDI synthesizer (for example, an Adlib®-compatible or OPL/3 FM synthesizer).
            MIXERLINE_COMPONENTTYPE_SRC_SYNTHESIZER
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.SourceCompactDisc">
            <summary>
            Audio line is a source originating from the output of an internal audio CD. This component type is provided for audio cards that provide an audio source line intended to be connected to an audio CD (or CD-ROM playing an audio CD).
            MIXERLINE_COMPONENTTYPE_SRC_COMPACTDISC
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.SourceTelephone">
            <summary>
            Audio line is a source originating from an incoming telephone line.
            MIXERLINE_COMPONENTTYPE_SRC_TELEPHONE
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.SourcePcSpeaker">
            <summary>
            Audio line is a source originating from personal computer speaker. Several audio cards for personal computers provide the ability to mix what would typically be played on the internal speaker with the output of an audio card. Some audio cards support the ability to use this output as a recording source.
            MIXERLINE_COMPONENTTYPE_SRC_PCSPEAKER
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.SourceWaveOut">
            <summary>
            Audio line is a source originating from the waveform-audio output digital-to-analog converter (DAC). Most audio cards for personal computers provide this component type as a source to the MIXERLINE_COMPONENTTYPE_DST_SPEAKERS destination. Some cards also allow this source to be routed to the MIXERLINE_COMPONENTTYPE_DST_WAVEIN destination.
            MIXERLINE_COMPONENTTYPE_SRC_WAVEOUT
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.SourceAuxiliary">
            <summary>
            Audio line is a source originating from the auxiliary audio line. This line type is intended as a source with gain or attenuation that can be routed to the MIXERLINE_COMPONENTTYPE_DST_SPEAKERS destination and/or recorded from the MIXERLINE_COMPONENTTYPE_DST_WAVEIN destination.
            MIXERLINE_COMPONENTTYPE_SRC_AUXILIARY
            </summary>
        </member>
        <member name="F:NAudio.Mixer.MixerLineComponentType.SourceAnalog">
            <summary>
            Audio line is an analog source (for example, analog output from a video-cassette tape).
            MIXERLINE_COMPONENTTYPE_SRC_ANALOG
            </summary>
        </member>
        <member name="T:NAudio.Mixer.SignedMixerControl">
            <summary>
            Represents a signed mixer control
            </summary>
        </member>
        <member name="M:NAudio.Mixer.SignedMixerControl.GetDetails(System.IntPtr)">
            <summary>
            Gets details for this contrl
            </summary>
        </member>
        <member name="P:NAudio.Mixer.SignedMixerControl.Value">
            <summary>
            The value of the control
            </summary>
        </member>
        <member name="P:NAudio.Mixer.SignedMixerControl.MinValue">
            <summary>
            Minimum value for this control
            </summary>
        </member>
        <member name="P:NAudio.Mixer.SignedMixerControl.MaxValue">
            <summary>
            Maximum value for this control
            </summary>
        </member>
        <member name="P:NAudio.Mixer.SignedMixerControl.Percent">
            <summary>
            Value of the control represented as a percentage
            </summary>
        </member>
        <member name="M:NAudio.Mixer.SignedMixerControl.ToString">
            <summary>
            String Representation for debugging purposes
            </summary>
            <returns></returns>
        </member>
        <member name="T:NAudio.Mixer.UnsignedMixerControl">
            <summary>
            Represents an unsigned mixer control
            </summary>
        </member>
        <member name="M:NAudio.Mixer.UnsignedMixerControl.GetDetails(System.IntPtr)">
            <summary>
            Gets the details for this control
            </summary>
        </member>
        <member name="P:NAudio.Mixer.UnsignedMixerControl.Value">
            <summary>
            The control value
            </summary>
        </member>
        <member name="P:NAudio.Mixer.UnsignedMixerControl.MinValue">
            <summary>
            The control's minimum value
            </summary>
        </member>
        <member name="P:NAudio.Mixer.UnsignedMixerControl.MaxValue">
            <summary>
            The control's maximum value
            </summary>
        </member>
        <member name="P:NAudio.Mixer.UnsignedMixerControl.Percent">
            <summary>
            Value of the control represented as a percentage
            </summary>
        </member>
        <member name="M:NAudio.Mixer.UnsignedMixerControl.ToString">
            <summary>
            String Representation for debugging purposes
            </summary>
        </member>
    </members>
</doc>
