{"About": "Hakkında", "AccentColor": "Accent Color", "Add": "Add", "AlwaysOnTop": "Her zaman Ü<PERSON>te", "Audio": "Ses", "AudioFormat": "Audio Format", "AudioSaved": "<PERSON><PERSON>", "BackColor": "Arka Renk", "BorderColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BorderThickness": "<PERSON><PERSON><PERSON><PERSON>r <PERSON>", "Bottom": "Alt", "CaptureDuration": "Zaman Aralığını Kaydet (saniye)", "Center": "Orta", "Changelog": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Clear": "<PERSON><PERSON><PERSON>", "ClearRecentList": "<PERSON>", "Clipboard": "Pan<PERSON>", "Close": "Ka<PERSON><PERSON>", "Color": "Renk", "ConfigCodecs": "Configure Codecs", "Configure": "Ö<PERSON>leş<PERSON>r", "CopyOutPathClipboard": "Çıktı dosyası konumunu Panoya kopyala", "CopyPath": "<PERSON><PERSON><PERSON><PERSON>", "CopyToClipboard": "<PERSON><PERSON>", "CornerRadius": "Renk Etki Alanı", "CrashLogs": "Crash Logs", "Crop": "Crop", "CustomSize": "Custom Size", "CustomUrl": "Custom Url", "DarkTheme": "Dark Theme", "Delete": "Sil", "DiscardChanges": "Discard Changes", "Disk": "Disk", "Donate": "Bağış Yap", "DownloadFFmpeg": "Download FFmpeg", "Edit": "Edit", "Elapsed": "Elapsed", "ErrorOccurred": "Bir Hata Oluştu", "Exit": "Çıkış", "FFmpegFolder": "FFmpeg Klasörü", "FFmpegLog": "FFmpeg Geçmişi", "FileMenu": "File", "FileMenuNew": "New", "FileMenuOpen": "Open", "FileMenuSave": "Save", "FileNaming": "File Naming", "Flip": "<PERSON><PERSON><PERSON>", "FontSize": "<PERSON><PERSON>", "FrameRate": "FPS", "FullScreen": "<PERSON>", "HideOnFullScreenShot": "Tam Ekranda Ekran Görüntüsü Alındığında Gizle", "Horizontal": "<PERSON><PERSON><PERSON>", "Host": "<PERSON><PERSON><PERSON>", "Hotkeys": "K<PERSON><PERSON>ollar", "ImageEditor": "Image Editor", "ImgEmpty": "Kaydedilmedi. <PERSON><PERSON><PERSON>aj <PERSON>", "ImgFormat": "İmaj <PERSON>atı", "ImgSavedClipboard": "<PERSON><PERSON><PERSON>", "ImgurFailed": "I<PERSON>gur Yüklemesi Başarısız", "ImgurSuccess": "<PERSON><PERSON><PERSON>r Yüklemesi Başarılı", "ImgurUploading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "IncludeClicks": "Fare Tıklamalarını Dahil Et", "IncludeCursor": "İmleci <PERSON>", "IncludeKeys": "Tuş Vuruşlarını Dahil Et", "Keymap": "Keymap", "Keystrokes": "Tuş Vuruşları", "KeystrokesHistoryCount": "History Count", "KeystrokesHistorySpacing": "History Spacing", "KeystrokesSeparateFile": "Save to separate Text file", "Language": "Dil", "Left": "Sol", "LoopbackSource": "Hoparlör Çıkış Kaynağı", "MaxRecent": "Kalacak maks. öğe sayı<PERSON>ı", "MaxTextLength": "Maks. Yazı Uzunluğu", "MicSource": "Mikrofon <PERSON>", "MinCapture": "Kaydetme Başlangıcında Küçült", "Minimize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MinTray": "Sistem Tepsisine <PERSON>", "MinTrayStartup": "Minimize to Tray on Startup", "MinTrayClose": "Minimize to Tray when Closed", "MouseClicks": "Fare Tıklamaları", "MouseMiddleClickColor": "Middle Click Color", "MousePointer": "<PERSON>", "MouseRightClickColor": "Right Click Color", "NewWindow": "NewWindow", "No": "Hay<PERSON><PERSON>", "None": "None", "Notifications": "Notifications", "NotSaved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NoWebcam": "Webcam Yok", "Ok": "<PERSON><PERSON>", "OnlyAudio": "Yalnızca Ses", "Opacity": "Opacity", "OpenFromClipboard": "Open from Clipboard", "OpenOutFolder": "Çıktı Klasörünü Aç", "OutFolder": "Çıktı Klasörü", "Overlays": "Overlays", "Padding": "Padding", "Password": "Pa<PERSON><PERSON>", "Paused": "<PERSON><PERSON>t Duraklatıldı", "PauseResume": "Durak<PERSON> | Devam Et", "PauseResumeRecording": "Kaydı Duraklat/Devam Et", "PlayRecAudio": "Playback recorded audio in real-time", "Port": "Port", "Preview": "Preview", "PreStartCountdown": "Pre Start Countdown (seconds)", "Proxy": "Proxy", "Quality": "<PERSON><PERSON>", "Radius": "E<PERSON><PERSON>", "Recent": "<PERSON>", "RecordStop": "<PERSON><PERSON> | Dur", "Redo": "Redo", "Refresh": "<PERSON><PERSON><PERSON>", "Region": "<PERSON><PERSON><PERSON>", "RegionSelector": "<PERSON><PERSON><PERSON>", "RemoveFromList": "<PERSON><PERSON>", "Reset": "Sıfırla", "Resize": "Boyutlandır", "RestoreDefaults": "<PERSON><PERSON>", "Right": "Sağ", "Rotate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SaveToClipboard": "Save to Clipboard", "Screen": "<PERSON><PERSON><PERSON>", "ScreenShot": "Ekran Görüntüsü", "ScreenShotActiveWindow": "Aktif Pencerenin Ekran Görüntüsünü Al", "ScreenShotDesktop": "Masaüstünün Ekran Görüntüsünü Al", "ScreenShotSaved": "Ekran Görüntüsü Kaydedildi", "ScreenShotTransforms": "Ekran Görüntüsü Dönüşümleri", "SelectFFmpegFolder": "FFmpeg Klasörünü Seç", "SelectOutFolder": "Çıktı Klasörünü Seç", "SeparateAudioFiles": "Separate files for every audio source", "ShowSysNotify": "Sistem Tepsisi Bildirimlerini Göster", "SnapToWindow": "Snap to Window", "Sounds": "Sounds", "StartStopRecording": "<PERSON><PERSON><PERSON>/Dur", "StreamingKeys": "Streaming Keys", "Timeout": "Zaman Aşımı", "ToggleMouseClicks": "Toggle Mouse Clicks", "ToggleKeystrokes": "Toggle Keystrokes", "Tools": "Tools", "Top": "Üst", "TrayIcon": "Tray Icon", "Trim": "<PERSON><PERSON>", "Undo": "Undo", "UploadToImgur": "Upload to Imgur", "UseProxyAuth": "Proxy Kimlik Doğrulaması Kullan", "UserName": "Kullanıcı Adı", "VarFrameRate": "Değişken Kare Hızı", "Vertical": "<PERSON><PERSON>", "Video": "Video", "VideoEncoder": "Video Kodlayıcı", "VideoSaved": "Video Kaydedildi", "VideoSource": "Video Kaynağı", "ViewCrashLogs": "View Crash Logs", "ViewLicenses": "View Licenses", "ViewOnGitHub": "View on GitHub", "WantToTranslate": "<PERSON><PERSON><PERSON><PERSON> etmek ister misin?", "WebCam": "Webcam", "WebCamSeparateFile": "Record Webcam to separate file", "WebCamView": "WebCam Görü<PERSON>ü<PERSON>ü", "Website": "Website", "Window": "<PERSON><PERSON><PERSON>", "Yes": "<PERSON><PERSON>"}