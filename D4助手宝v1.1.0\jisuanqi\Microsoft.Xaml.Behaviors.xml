<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Xaml.Behaviors</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Xaml.Behaviors.AttachableCollection`1">
            <summary>
            Represents a collection of IAttachedObject with a shared AssociatedObject and provides change notifications to its contents when that AssociatedObject changes.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.AttachableCollection`1.AssociatedObject">
            <summary>
            The object on which the collection is hosted.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.AttachableCollection`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.AttachableCollection`1"/> class.
            </summary>
            <remarks>Internal, because this should not be inherited outside this assembly.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.AttachableCollection`1.OnAttached">
            <summary>
            Called immediately after the collection is attached to an AssociatedObject.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.AttachableCollection`1.OnDetaching">
            <summary>
            Called when the collection is being detached from its AssociatedObject, but before it has actually occurred.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.AttachableCollection`1.ItemAdded(`0)">
            <summary>
            Called when a new item is added to the collection.
            </summary>
            <param name="item">The new item.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.AttachableCollection`1.ItemRemoved(`0)">
            <summary>
            Called when an item is removed from the collection.
            </summary>
            <param name="item">The removed item.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.AttachableCollection`1.VerifyAdd(`0)">
            <exception cref="T:System.InvalidOperationException">Cannot add the instance to a collection more than once.</exception>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.AttachableCollection`1.Microsoft#Xaml#Behaviors#IAttachedObject#AssociatedObject">
            <summary>
            Gets the associated object.
            </summary>
            <value>The associated object.</value>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.AttachableCollection`1.Attach(System.Windows.DependencyObject)">
            <summary>
            Attaches to the specified object.
            </summary>
            <param name="dependencyObject">The object to attach to.</param>
            <exception cref="T:System.InvalidOperationException">The IAttachedObject is already attached to a different object.</exception>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.AttachableCollection`1.Detach">
            <summary>
            Detaches this instance from its associated object.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Behavior`1">
            <summary>
            Encapsulates state information and zero or more ICommands into an attachable object.
            </summary>
            <typeparam name="T">The type the <see cref="T:Microsoft.Xaml.Behaviors.Behavior`1"/> can be attached to.</typeparam>
            <remarks>
            	Behavior is the base class for providing attachable state and commands to an object.
            	The types the Behavior can be attached to can be controlled by the generic parameter.
            	Override OnAttached() and OnDetaching() methods to hook and unhook any necessary handlers
            	from the AssociatedObject.
            </remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Behavior`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.Behavior`1"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Behavior`1.AssociatedObject">
            <summary>
            Gets the object to which this <see cref="T:Microsoft.Xaml.Behaviors.Behavior`1"/> is attached.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Behavior">
            <summary>
            Encapsulates state information and zero or more ICommands into an attachable object.
            </summary>
            <remarks>This is an infrastructure class. Behavior authors should derive from Behavior&lt;T&gt; instead of from this class.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Behavior.AssociatedType">
            <summary>
            The type to which this behavior can be attached.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Behavior.AssociatedObject">
            <summary>
            Gets the object to which this behavior is attached.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Behavior.OnAttached">
            <summary>
            Called after the behavior is attached to an AssociatedObject.
            </summary>
            <remarks>Override this to hook up functionality to the AssociatedObject.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Behavior.OnDetaching">
            <summary>
            Called when the behavior is being detached from its AssociatedObject, but before it has actually occurred.
            </summary>
            <remarks>Override this to unhook functionality from the AssociatedObject.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Behavior.Microsoft#Xaml#Behaviors#IAttachedObject#AssociatedObject">
            <summary>
            Gets the associated object.
            </summary>
            <value>The associated object.</value>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Behavior.Attach(System.Windows.DependencyObject)">
            <summary>
            Attaches to the specified object.
            </summary>
            <param name="dependencyObject">The object to attach to.</param>
            <exception cref="T:System.InvalidOperationException">The Behavior is already hosted on a different element.</exception>
            <exception cref="T:System.InvalidOperationException">dependencyObject does not satisfy the Behavior type constraint.</exception>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Behavior.Detach">
            <summary>
            Detaches this instance from its associated object.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.BehaviorCollection">
            <summary>
            Represents a collection of behaviors with a shared AssociatedObject and provides change notifications to its contents when that AssociatedObject changes.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.BehaviorCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.BehaviorCollection"/> class.
            </summary>
            <remarks>Internal, because this should not be inherited outside this assembly.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.BehaviorCollection.OnAttached">
            <summary>
            Called immediately after the collection is attached to an AssociatedObject.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.BehaviorCollection.OnDetaching">
            <summary>
            Called when the collection is being detached from its AssociatedObject, but before it has actually occurred.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.BehaviorCollection.ItemAdded(Microsoft.Xaml.Behaviors.Behavior)">
            <summary>
            Called when a new item is added to the collection.
            </summary>
            <param name="item">The new item.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.BehaviorCollection.ItemRemoved(Microsoft.Xaml.Behaviors.Behavior)">
            <summary>
            Called when an item is removed from the collection.
            </summary>
            <param name="item">The removed item.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.BehaviorCollection.CreateInstanceCore">
            <summary>
            Creates a new instance of the BehaviorCollection.
            </summary>
            <returns>The new instance.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.ComparisonLogic.EvaluateImpl(System.Object,Microsoft.Xaml.Behaviors.Core.ComparisonConditionType,System.Object)">
            <summary>
            This method evaluates operands. 
            </summary>
            <param name="leftOperand">Left operand from the LeftOperand property.</param>
            <param name="operatorType">Operator from Operator property.</param>
            <param name="rightOperand">Right operand from the RightOperand property.</param>
            <returns>Returns true if the condition is met; otherwise, returns false.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.ComparisonLogic.EvaluateComparable(System.IComparable,Microsoft.Xaml.Behaviors.Core.ComparisonConditionType,System.IComparable)">
            <summary>
            Evaluates both operands that implement the IComparable interface.
            </summary>
            <param name="leftOperand">Left operand from the LeftOperand property.</param>
            <param name="operatorType">Operator from Operator property.</param>
            <param name="rightOperand">Right operand from the RightOperand property.</param>
            <returns>Returns true if the condition is met; otherwise, returns false.</returns>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.ActionCommand">
            <summary>
            A basic implementation of ICommand that wraps a method that takes no parameters or a method that takes one parameter.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ActionCommand.#ctor(System.Action)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.Core.ActionCommand"/> class.
            </summary>
            <param name="action">The action.</param>
            <remarks>Use this constructor to provide an action that ignores the ICommand parameter.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ActionCommand.#ctor(System.Action{System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.Core.ActionCommand"/> class.
            </summary>
            <param name="objectAction">An action that takes an object parameter.</param>
            <remarks>Use this constructor to provide an action that uses the object parameter passed by the Execute method.</remarks>
        </member>
        <member name="E:Microsoft.Xaml.Behaviors.Core.ActionCommand.System#Windows#Input#ICommand#CanExecuteChanged">
            <summary>
            Occurs when changes occur that affect whether the command should execute. Will not be fired by ActionCommand.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ActionCommand.System#Windows#Input#ICommand#CanExecute(System.Object)">
            <summary>
            Defines the method that determines whether the command can execute in its current state.
            </summary>
            <param name="parameter">Data used by the command.  If the command does not require data to be passed, then this object can be set to null.</param>
            <returns>
            Always returns true.
            </returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ActionCommand.Execute(System.Object)">
            <summary>
            Defines the method to be called when the command is invoked.
            </summary>
            <param name="parameter">Data used by the command.  If the command does not require data to be passed, then this object can be set to null.</param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.CallMethodAction">
            <summary>
            Calls a method on a specified object when invoked.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.CallMethodAction.TargetObject">
            <summary>
            The object that exposes the method of interest. This is a dependency property.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.CallMethodAction.MethodName">
            <summary>
            The name of the method to invoke. This is a dependency property.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.CallMethodAction.Invoke(System.Object)">
            <summary>
            Invokes the action.
            </summary>
            <param name="parameter">The parameter of the action. If the action does not require a parameter, the parameter may be set to a null reference.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.CallMethodAction.OnAttached">
            <summary>
            Called after the action is attached to an AssociatedObject.
            </summary>
            <remarks>Override this to hook up functionality to the AssociatedObject.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.CallMethodAction.OnDetaching">
            <summary>
            Called when the action is getting detached from its AssociatedObject, but before it has actually occurred.
            </summary>
            <remarks>Override this to unhook functionality from the AssociatedObject.</remarks>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.ChangePropertyAction">
            <summary>
            An action that will change a specified property to a specified value when invoked.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ChangePropertyAction.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.Core.ChangePropertyAction"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.ChangePropertyAction.PropertyName">
            <summary>
            Gets or sets the name of the property to change. This is a dependency property.
            </summary>
            <value>The name of the property to change.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.ChangePropertyAction.Value">
            <summary>
            Gets or sets the value to set. This is a dependency property.
            </summary>
            <value>The value to set.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.ChangePropertyAction.Duration">
            <summary>
            Gets or sets the duration of the animation that will occur when the ChangePropertyAction is invoked.  This is a dependency property.
            If the duration is unset, no animation will be applied.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.ChangePropertyAction.Increment">
            <summary>
            Increment by Value if true; otherwise, set the value directly. If the property cannot be incremented, it will instead try to set the value directly.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ChangePropertyAction.Invoke(System.Object)">
            <summary>
            Invokes the action.
            </summary>
            <param name="parameter">The parameter of the action. If the action does not require a parameter, then the parameter may be set to a null reference.</param>
            <exception cref="T:System.ArgumentException">A property with <c cref="P:Microsoft.Xaml.Behaviors.Core.ChangePropertyAction.PropertyName"/> could not be found on the Target.</exception>
            <exception cref="T:System.ArgumentException">Could not set <c cref="P:Microsoft.Xaml.Behaviors.Core.ChangePropertyAction.PropertyName"/> to the value specified by <c cref="P:Microsoft.Xaml.Behaviors.Core.ChangePropertyAction.Value"/>.</exception>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.ComparisonCondition">
            <summary>
            Represents one ternary condition.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.ComparisonCondition.LeftOperand">
            <summary>
            Gets or sets the left operand.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.ComparisonCondition.RightOperand">
            <summary>
            Gets or sets the right operand.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.ComparisonCondition.Operator">
            <summary>
            Gets or sets the comparison operator. 
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ComparisonCondition.Evaluate">
            <summary>
            Method that evaluates the condition. Note that this method can throw ArgumentException if the operator is
            incompatible with the type. For instance, operators LessThan, LessThanOrEqual, GreaterThan, and GreaterThanOrEqual
            require both operators to implement IComparable. 
            </summary>
            <returns>Returns true if the condition has been met; otherwise, returns false.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ComparisonCondition.EnsureBindingUpToDate">
            <summary>
            Ensure that any binding on DP operands are up-to-date.  
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.ComparisonConditionType">
            <summary>
            Enumeration of different comparison operators.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.ForwardChaining">
            <summary>
            Forward chaining.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.ConditionalExpression">
            <summary>
            Represents a conditional expression that is set on a ConditionBehavior.Condition property. 
            Contains a list of conditions that gets evaluated in order to return true or false for ICondition.Evaluate().
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.ConditionalExpression.ForwardChaining">
            <summary>
            Gets or sets forward chaining for the conditions.
            If forward chaining is set to ForwardChaining.And, all conditions must be met.
            If forward chaining is set to ForwardChaining.Or, only one condition must be met.		
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.ConditionalExpression.Conditions">
            <summary>
            Return the Condition collections.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ConditionalExpression.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.Core.ConditionalExpression"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ConditionalExpression.Evaluate">
            <summary>
            Goes through the Conditions collection and evalutes each condition based on 
            ForwardChaining property.
            </summary>
            <returns>Returns true if conditions are met; otherwise, returns false.</returns>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.ConditionBehavior">
            <summary>
            A behavior that attaches to a trigger and controls the conditions
            to fire the actions. 
            </summary>
            
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.ConditionBehavior.Condition">
            <summary>
            Gets or sets the IConditon object on behavior.
            </summary>
            <value>The name of the condition to change.</value>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ConditionBehavior.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.Core.ConditionBehavior"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ConditionBehavior.OnPreviewInvoke(System.Object,Microsoft.Xaml.Behaviors.PreviewInvokeEventArgs)">
            <summary>
            The event handler that is listening to the preview invoke event that is fired by 
            the trigger. Setting PreviewInvokeEventArgs.Cancelling to True will
            cancel the invocation.
            </summary>
            <param name="sender">The trigger base object.</param>
            <param name="e">An object of type PreviewInvokeEventArgs where e.Cancelling can be set to True.</param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.DataStateBehavior">
            <summary>
            Toggles between two states based on a conditional statement.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.DataStateBehavior.Binding">
            <summary>
            Gets or sets the binding that produces the property value of the data object. This is a dependency property.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.DataStateBehavior.Value">
            <summary>
            Gets or sets the value to be compared with the property value of the data object. This is a dependency property.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.DataStateBehavior.TrueState">
            <summary>
            Gets or sets the name of the visual state to transition to when the condition is met. This is a dependency property.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.DataStateBehavior.FalseState">
            <summary>
            Gets or sets the name of the visual state to transition to when the condition is not met. This is a dependency property.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.DataStateBehavior.OnAttached">
            <summary>
            Called after the behavior is attached to an AssociatedObject.
            </summary>
            <remarks>Override this to hook up functionality to the AssociatedObject.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.DataStateBehavior.IsElementLoaded(System.Windows.FrameworkElement)">
            <summary>
            A helper function to take the place of FrameworkElement.IsLoaded, as this property isn't available in Silverlight.
            </summary>
            <param name="element">The element of interest.</param>
            <returns>Returns true if the element has been loaded; otherwise, returns false.</returns>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.DataStoreChangedTrigger">
            <summary>
            Trigger designed to be bound to a data store property. Fires when the property changes.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.DataTrigger">
            <summary>
            Represents a trigger that performs actions when the bound data meets a specified condition.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.DataTrigger.Value">
            <summary>
            Gets or sets the value to be compared with the property value of the data object. This is a dependency property.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.DataTrigger.Comparison">
            <summary>
            Gets or sets the type of comparison to be performed between the specified values. This is a dependency property.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.DataTrigger.EvaluateBindingChange(System.Object)">
            <summary>
            Called when the binding property has changed. 
            UA_REVIEW:chabiss
            </summary>
            <param name="args"><see cref="T:System.Windows.DependencyPropertyChangedEventArgs"/> argument.</param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager">
            <summary>
            ExtendedVisualStateManager is a custom VisualStateManager that can smooth out the animation of layout properties.
            With this custom VisualStateManager, states can include changes to properties like Grid.Column, can change element heights to or from Auto, and so on.
            These changes will be smoothed out over time using the GeneratedDuration and GeneratedEasingFunction of the appropriate transition.
            See the "VisualStateManager overrides" region below for a general description of the algorithm.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.OriginalLayoutValueRecord">
            <summary>
            OriginalValueRecord remembers the original value of a property that was changed in a state.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.UseFluidLayoutProperty">
            <summary>
            A VisualStateGroup that can use FluidLayout or not.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.RuntimeVisibilityPropertyProperty">
            <summary>
            Visibility is shadowed by a custom attached property at runtime.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.OriginalLayoutValuesProperty">
            <summary>
            A VisualStateGroup keeps a list of these original values in an attached property.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.LayoutStoryboardProperty">
            <summary>
            For every state, the layout-specific properties get extracted and then are attached to the state. These properties are removed from the state itself.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.CurrentStateProperty">
            <summary>
            Remember the current state.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.TransitionEffectProperty">
            <summary>
            The TransitionEffect to use when the state changes.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.TransitionEffectStoryboardProperty">
            <summary>
            The TransitionEffectStoryboard in use during the state change.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.DidCacheBackgroundProperty">
            <summary>
            The cached background in use during the state change.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.CachedBackgroundProperty">
            <summary>
            The cached background in use during the state change.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.CachedEffectProperty">
            <summary>
            The cached background in use during the state change.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.MovingElements">
            <summary>
            This is the set of elements that are currently in motion.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.LayoutTransitionStoryboard">
            <summary>
            This is the storyboard that is animating the transition.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.LayoutProperties">
            <summary>
            This list contains all the known layout properties.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.StopAnimations">
            <summary>
            Stop the animation and replace the layout changes that were made to support that animation.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.FindTransition(System.Windows.VisualStateGroup,System.Windows.VisualState,System.Windows.VisualState)">
            <summary>
            Locate the transition that VisualStateManager will use to animate the change, so that the layout animation can match the duration and easing.
            </summary>
            <param name="group">The group in which the transition is taking place.</param>
            <param name="previousState">The state that you are coming from.</param>
            <param name="state">The state you are going to.</param>
            <returns>The transition</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.ExtractLayoutStoryboard(System.Windows.VisualState)">
            <summary>
            Remove all layout-affecting properties from the Storyboard for the state and cache them in an attached property.
            </summary>
            <param name="state">The state you are moving to.</param>
            <returns>A Storyboard containing the layout properties in that state.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.FindTargetElements(System.Windows.FrameworkElement,System.Windows.FrameworkElement,System.Windows.Media.Animation.Storyboard,System.Collections.Generic.List{Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.OriginalLayoutValueRecord},System.Collections.Generic.List{System.Windows.FrameworkElement})">
            <summary>
            The set of target elements is the set of all elements that might have moved in a layout transition. This set is the closure of:
             - Elements with layout properties animated in the state.
             - Siblings of elements in the set.
             - Parents of elements in the set.
             
            Subsequent code will check these rectangles both before and after the layout change.
            </summary>
            <param name="control">The control whose layout is changing state.</param>
            <param name="templateRoot">The template root.</param>
            <param name="layoutStoryboard">The storyboard containing the layout changes.</param>
            <param name="originalValueRecords">Any previous values from previous state navigations that might be reverted.</param>
            <param name="movingElements">The set of elements currently in motion, if there is a state change transition ongoing.</param>
            <returns>The full set of elements whose layout may have changed.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.GetRectsOfTargets(System.Collections.Generic.List{System.Windows.FrameworkElement},System.Collections.Generic.List{System.Windows.FrameworkElement})">
            <summary>
            Gets a set of rectangles for all the elements in the target list.
            </summary>
            <param name="targets">The set of elements to consider.</param>
            <param name="movingElements">The set of elements currently in motion.</param>
            <returns>A Dictionary mapping elements to their Rects.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.GetLayoutRect(System.Windows.FrameworkElement)">
            <summary>
            Get the layout rectangle of an element, by getting the layout slot and then computing which portion of the slot is being used.
            </summary>
            <param name="element">The element whose layout Rect will be retrieved.</param>
            <returns>The layout Rect of that element.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.GetOldOpacities(System.Windows.FrameworkElement,System.Windows.FrameworkElement,System.Windows.Media.Animation.Storyboard,System.Collections.Generic.List{Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.OriginalLayoutValueRecord},System.Collections.Generic.List{System.Windows.FrameworkElement})">
            <summary>
            Get the opacities of elements at the time of the state change, instead of visibilities, because the state change may be in process and the current value is the most important.
            </summary>
            <param name="control">The control whose state is changing.</param>
            <param name="templateRoot">The template root.</param>
            <param name="layoutStoryboard">The storyboard with the layout properties.</param>
            <param name="originalValueRecords">The set of original values.</param>
            <param name="movingElements">The elements currently in motion</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.SetLayoutStoryboardProperties(System.Windows.FrameworkElement,System.Windows.FrameworkElement,System.Windows.Media.Animation.Storyboard,System.Collections.Generic.List{Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.OriginalLayoutValueRecord})">
            <summary>
            Go through the layout Storyboard and set all the properties by using SetValue to enable calling UpdateLayout without
            ticking the timeline, which would cause a render.
            All values that are overwritten will be stored in the collection of OriginalValueRecords so that they can be replaced later.
            </summary>
            <param name="control">The control whose state is changing.</param>
            <param name="templateRoot">The template root.</param>
            <param name="layoutStoryboard">The Storyboard holding the layout properties.</param>
            <param name="originalValueRecords">The store of original values.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.WrapMovingElementsInCanvases(System.Collections.Generic.List{System.Windows.FrameworkElement},System.Collections.Generic.Dictionary{System.Windows.FrameworkElement,System.Windows.Rect},System.Collections.Generic.Dictionary{System.Windows.FrameworkElement,System.Windows.Rect})">
            <summary>
            Take all the elements that will be moving as a result of the layout animation, and wrap them in Canvas panels so that
            they do not affect their sibling elements.
            </summary>
            <param name="movingElements">The set of elements that will be moving.</param>
            <param name="oldRects">The old Rect.</param>
            <param name="newRects">The new Rect.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.UnwrapMovingElementsFromCanvases(System.Collections.Generic.List{System.Windows.FrameworkElement})">
            <summary>
            Take all the elements that have been moving as a result of the layout animation, and unwrap them from their Canvas panels.
            </summary>
            <param name="movingElements">The set of elements that have been moving.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.CopyLayoutProperties(System.Windows.FrameworkElement,System.Windows.FrameworkElement,System.Boolean)">
            <summary>
            Copy the layout properties from the source element to the target element, clearing them from the source.
            </summary>
            <param name="source">The source of the layout properties.</param>
            <param name="target">The destination of the layout properties.</param>
            <param name="restoring">A flag to indicate if a restore is occuring.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.ExtendedVisualStateManager.CreateLayoutTransitionStoryboard(System.Windows.VisualTransition,System.Collections.Generic.List{System.Windows.FrameworkElement},System.Collections.Generic.Dictionary{System.Windows.FrameworkElement,System.Double})">
            <summary>
            Create the actual Storyboard that will be used to animate the transition. Use all previously calculated results.
            </summary>
            <param name="transition">The transition.</param>
            <param name="movingElements">The set of elements that will be moving.</param>
            <param name="oldOpacities">The old opacities of the elements whose visibility properties are changing.</param>
            <returns>The Storyboard.</returns>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.GoToStateAction">
            <summary>
            An action that will transition a FrameworkElement to a specified VisualState when invoked.
            </summary>
            <remarks>
            If the TargetName property is set, this action will attempt to change the state of the targeted element. If not, it walks
            the element tree in an attempt to locate an alternative target that defines states. ControlTemplate and UserControl are 
            two common possibilities.
            </remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.GoToStateAction.UseTransitions">
            <summary>
            Determines whether or not to use a VisualTransition to transition between states.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.GoToStateAction.StateName">
            <summary>
            The name of the VisualState.  
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.GoToStateAction.OnTargetChanged(System.Windows.FrameworkElement,System.Windows.FrameworkElement)">
            <summary>
            Called when the target changes. If the TargetName property isn't set, this action has custom behavior.
            </summary>
            <param name="oldTarget"></param>
            <param name="newTarget"></param>
            <exception cref="T:System.InvalidOperationException">Could not locate an appropriate FrameworkElement with states.</exception>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.GoToStateAction.Invoke(System.Object)">
            <summary>
            This method is called when some criteria is met and the action is invoked.
            </summary>
            <param name="parameter"></param>
            <exception cref="T:System.InvalidOperationException">Could not change the target to the specified StateName.</exception>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.ICondition">
            <summary>
            An interface that a given object must implement in order to be 
            set on a ConditionBehavior.Condition property. 
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.LaunchUriOrFileAction">
            <summary>
            An action that will launch a process to open a file or Uri. For files, this action will launch the default program 
            for the given file extension. A Uri will open in a web browser.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.LaunchUriOrFileAction.Path">
            <summary>
            The file or Uri to open. 
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.LaunchUriOrFileAction.Invoke(System.Object)">
            <summary>
            This method is called when some criteria is met and the action is invoked.
            </summary>
            <param name="parameter"></param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.PropertyChangedTrigger">
            <summary>
            Represents a trigger that performs actions when the bound data have changed. 
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.PropertyChangedTrigger.Binding">
            <summary>
            A binding object that the trigger will listen to, and that causes the trigger to fire when it changes.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.PropertyChangedTrigger.EvaluateBindingChange(System.Object)">
            <summary>
            Called when the binding property has changed. 
            </summary>
            <param name="args"><see cref="T:System.Windows.DependencyPropertyChangedEventArgs"/> argument.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.PropertyChangedTrigger.OnAttached">
            <summary>
            Called after the trigger is attached to an AssociatedObject.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.PropertyChangedTrigger.OnDetaching">
            <summary>
            Called when the trigger is being detached from its AssociatedObject, but before it has actually occurred.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.NavigationMenuAction.OnTargetChanged(System.Windows.FrameworkElement,System.Windows.FrameworkElement)">
            <summary>
            Called when the target changes. If the TargetName property isn't set, this action has custom behavior.
            </summary>
            <param name="oldTarget"></param>
            <param name="newTarget"></param>
            <exception cref="T:System.InvalidOperationException">Could not locate an appropriate FrameworkElement with states.</exception>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.RemoveItemInListBoxAction">
            <summary>
            Allows a user to remove the item from a ListBox ItemTemplate.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.RemoveElementAction">
            <summary>
            An action that will remove the targeted element from the tree when invoked.
            </summary>
            <remarks>
            This action may fail. The action understands how to remove elements from common parents but not from custom collections or direct manipulation
            of the visual tree.
            </remarks>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.SetDataStoreValueAction">
            <summary>
            An action that will change the value of a property from a data store object.
            This class is identical to ChangePropertyAction. The only difference is that the data store picker is loaded 
            for this action.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Core.TimerTrigger">
            <summary>
            A trigger that is triggered by a specified event occurring on its source and fires after a delay when that event is fired.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Core.TimerTrigger.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.Core.TimerTrigger"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.TimerTrigger.MillisecondsPerTick">
            <summary>
            Gets or sets the number of milliseconds to wait between ticks. This is a dependency property.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Core.TimerTrigger.TotalTicks">
            <summary>
            Gets or sets the total number of ticks to be fired before the trigger is finished.  This is a dependency property.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.CustomPropertyValueEditor">
            <summary>
            Enumerates possible values for reusable property value editors.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.CustomPropertyValueEditor.Element">
            <summary>
            Uses the element picker, if supported, to edit this property at design time.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.CustomPropertyValueEditor.Storyboard">
            <summary>
            Uses the storyboard picker, if supported, to edit this property at design time.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.CustomPropertyValueEditor.StateName">
            <summary>
            Uses the state picker, if supported, to edit this property at design time.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.CustomPropertyValueEditor.ElementBinding">
            <summary>
            Uses the element-binding picker, if supported, to edit this property at design time.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.CustomPropertyValueEditor.PropertyBinding">
            <summary>
            Uses the property-binding picker, if supported, to edit this property at design time.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.CustomPropertyValueEditorAttribute">
            <summary>
            Associates the given editor type with the property on which the CustomPropertyValueEditor is applied.
            </summary>
            <remarks>Use this attribute to get improved design-time editing for properties that denote element (by name), storyboards, or states (by name).</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.CustomPropertyValueEditorAttribute.CustomPropertyValueEditor">
            <summary>
            Gets or sets the custom property value editor.
            </summary>
            <value>The custom property value editor.</value>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.CustomPropertyValueEditorAttribute.#ctor(Microsoft.Xaml.Behaviors.CustomPropertyValueEditor)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.CustomPropertyValueEditorAttribute"/> class.
            </summary>
            <param name="customPropertyValueEditor">The custom property value editor.</param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.DataBindingHelper">
            <summary>
            Helper class for managing binding expressions on dependency objects.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.DataBindingHelper.EnsureDataBindingUpToDateOnMembers(System.Windows.DependencyObject)">
            <summary>
            Ensure that all DP on an action with binding expressions are
            up to date. DataTrigger fires during data binding phase. Since
            actions are children of the trigger, any bindings on the action
            may not be up-to-date. This routine is called before the action
            is invoked in order to guarantee that all bindings are up-to-date
            with the most current data. 
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.DataBindingHelper.EnsureDataBindingOnActionsUpToDate(Microsoft.Xaml.Behaviors.TriggerBase{System.Windows.DependencyObject})">
            <summary>
            Ensures that all binding expression on actions are up to date
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.DataBindingHelper.EnsureBindingUpToDate(System.Windows.DependencyObject,System.Windows.DependencyProperty)">
            <summary>
             This helper function ensures that, if a dependency property on a dependency object
             has a binding expression, the binding expression is up-to-date. 
            </summary>
            <param name="target"></param>
            <param name="dp"></param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.DefaultTriggerAttribute">
            <summary>
            Provides design tools information about what <see cref="T:Microsoft.Xaml.Behaviors.TriggerBase"/> to instantiate for a given action or command.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.DefaultTriggerAttribute.TargetType">
            <summary>
            Gets the type that this DefaultTriggerAttribute applies to.
            </summary>
            <value>The type this DefaultTriggerAttribute applies to.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.DefaultTriggerAttribute.TriggerType">
            <summary>
            Gets the type of the <see cref="T:Microsoft.Xaml.Behaviors.TriggerBase"/> to instantiate.
            </summary>
            <value>The type of the <see cref="T:Microsoft.Xaml.Behaviors.TriggerBase"/> to instantiate.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.DefaultTriggerAttribute.Parameters">
            <summary>
            Gets the parameters to pass to the <see cref="T:Microsoft.Xaml.Behaviors.TriggerBase"/> constructor.
            </summary>
            <value>The parameters to pass to the <see cref="T:Microsoft.Xaml.Behaviors.TriggerBase"/> constructor.</value>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.DefaultTriggerAttribute.#ctor(System.Type,System.Type,System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.DefaultTriggerAttribute"/> class.
            </summary>
            <param name="targetType">The type this attribute applies to.</param>
            <param name="triggerType">The type of <see cref="T:Microsoft.Xaml.Behaviors.TriggerBase"/> to instantiate.</param>
            <param name="parameter">A single argument for the specified <see cref="T:Microsoft.Xaml.Behaviors.TriggerBase"/>.</param>
            <exception cref="T:System.ArgumentException"><c cref="F:Microsoft.Xaml.Behaviors.DefaultTriggerAttribute.triggerType"/> is not derived from TriggerBase.</exception>
            <remarks>This constructor is useful if the specifed <see cref="T:Microsoft.Xaml.Behaviors.TriggerBase"/> has a single argument. The
            resulting code will be CLS compliant.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.DefaultTriggerAttribute.#ctor(System.Type,System.Type,System.Object[])">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.DefaultTriggerAttribute"/> class.
            </summary>
            <param name="targetType">The type this attribute applies to.</param>
            <param name="triggerType">The type of <see cref="T:Microsoft.Xaml.Behaviors.TriggerBase"/> to instantiate.</param>
            <param name="parameters">The constructor arguments for the specified <see cref="T:Microsoft.Xaml.Behaviors.TriggerBase"/>.</param>
            <exception cref="T:System.ArgumentException"><c cref="F:Microsoft.Xaml.Behaviors.DefaultTriggerAttribute.triggerType"/> is not derived from TriggerBase.</exception>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.DefaultTriggerAttribute.Instantiate">
            <summary>
            Instantiates this instance.
            </summary>
            <returns>The <see cref="T:Microsoft.Xaml.Behaviors.TriggerBase"/> specified by the DefaultTriggerAttribute.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.DependencyObjectHelper.GetSelfAndAncestors(System.Windows.DependencyObject)">
            <summary>
            This method will use the VisualTreeHelper.GetParent method to do a depth first walk up 
            the visual tree and return all ancestors of the specified object, including the object itself.
            </summary>
            <param name="dependencyObject">The object in the visual tree to find ancestors of.</param>
            <returns>Returns itself an all ancestors in the visual tree.</returns>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.EventObserver">
            <summary>
            EventObserver is designed to help manage event handlers by detatching when disposed. Creating this object will also attach in the constructor.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventObserver.#ctor(System.Reflection.EventInfo,System.Object,System.Delegate)">
            <summary>
            Creates an instance of EventObserver and attaches to the supplied event on the supplied target. Call dispose to detach.
            </summary>
            <param name="eventInfo">The event to attach and detach from.</param>
            <param name="target">The target object the event is defined on. Null if the method is static.</param>
            <param name="handler">The delegate to attach to the event.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventObserver.Dispose">
            <summary>
            Detaches the handler from the event.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.EventTrigger">
            <summary>
            A trigger that listens for a specified event on its source and fires when that event is fired.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventTrigger.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.EventTrigger"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventTrigger.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.EventTrigger"/> class.
            </summary>
            <param name="eventName">Name of the event.</param>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.EventTrigger.EventName">
            <summary>
            Gets or sets the name of the event to listen for. This is a dependency property.
            </summary>
            <value>The name of the event.</value>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.EventTriggerBase`1">
            <summary>
            Represents a trigger that can listen to an element other than its AssociatedObject.
            </summary>
            <typeparam name="T">The type that this trigger can be associated with.</typeparam>
            <remarks>
            	EventTriggerBase extends TriggerBase to add knowledge of another object than the one it is attached to. 
            	This allows a user to attach a Trigger/Action pair to one element and invoke the Action in response to a 
            	change in another object somewhere else. Override OnSourceChanged to hook or unhook handlers on the source 
            	element, and OnAttached/OnDetaching for the associated element. The type of the Source element can be 
            	constrained by the generic type parameter. If you need control over the type of the 
            	AssociatedObject, set a TypeConstraintAttribute on your derived type.
            </remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventTriggerBase`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.EventTriggerBase`1"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.EventTriggerBase`1.Source">
            <summary>
            Gets the resolved source. If <c ref="SourceName"/> is not set or cannot be resolved, defaults to AssociatedObject.
            </summary>
            <value>The resolved source object.</value>
            <remarks>In general, this property should be used in place of AssociatedObject in derived classes.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventTriggerBase`1.OnSourceChanged(`0,`0)">
            <summary>
            Called when the source property changes.
            </summary>
            <remarks>Override this to hook functionality to and unhook functionality from the specified source, rather than the AssociatedObject.</remarks>
            <param name="oldSource">The old source.</param>
            <param name="newSource">The new source.</param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.EventTriggerBase">
            <summary>
            Represents a trigger that can listen to an object other than its AssociatedObject.
            </summary>
            <remarks>This is an infrastructure class. Trigger authors should derive from EventTriggerBase&lt;T&gt; instead of this class.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.EventTriggerBase.AssociatedObjectTypeConstraint">
            <summary>
            Gets the type constraint of the associated object.
            </summary>
            <value>The associated object type constraint.</value>
            <remarks>Define a TypeConstraintAttribute on a derived type to constrain the types it may be attached to.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.EventTriggerBase.SourceTypeConstraint">
            <summary>
            Gets the source type constraint.
            </summary>
            <value>The source type constraint.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.EventTriggerBase.SourceObject">
            <summary>
            Gets or sets the target object. If TargetObject is not set, the target will look for the object specified by TargetName. If an element referred to by TargetName cannot be found, the target will default to the AssociatedObject. This is a dependency property.
            </summary>
            <value>The target object.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.EventTriggerBase.SourceName">
            <summary>
            Gets or sets the name of the element this EventTriggerBase listens for as a source. If the name is not set or cannot be resolved, the AssociatedObject will be used.  This is a dependency property.
            </summary>
            <value>The name of the source element.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.EventTriggerBase.Source">
            <summary>
            Gets the resolved source. If <c ref="SourceName"/> is not set or cannot be resolved, defaults to AssociatedObject.
            </summary>
            <value>The resolved source object.</value>
            <remarks>In general, this property should be used in place of AssociatedObject in derived classes.</remarks>
            <exception cref="T:System.InvalidOperationException">The element pointed to by <c cref="P:Microsoft.Xaml.Behaviors.EventTriggerBase.Source"/> does not satisify the type constraint.</exception>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventTriggerBase.GetEventName">
            <summary>
            Specifies the name of the Event this EventTriggerBase is listening for.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventTriggerBase.OnEvent(System.EventArgs)">
            <summary>
            Called when the event associated with this EventTriggerBase is fired. By default, this will invoke all actions on the trigger.
            </summary>
            <param name="eventArgs">The <see cref="T:System.EventArgs"/> instance containing the event data.</param>
            <remarks>Override this to provide more granular control over when actions associated with this trigger will be invoked.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventTriggerBase.OnSourceChangedImpl(System.Object,System.Object)">
            <summary>
            Called when the source changes.
            </summary>
            <param name="oldSource">The old source.</param>
            <param name="newSource">The new source.</param>
            <remarks>This function should be overridden in derived classes to hook functionality to and unhook functionality from the changing source objects.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventTriggerBase.OnAttached">
            <summary>
            Called after the trigger is attached to an AssociatedObject.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventTriggerBase.OnDetaching">
            <summary>
            Called when the trigger is being detached from its AssociatedObject, but before it has actually occurred.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventTriggerBase.UnregisterLoaded(System.Windows.FrameworkElement)">
            <summary>
            Removes the event handler from the Loaded event of the associated object.
            </summary>
            <param name="associatedElement">The associated object</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.EventTriggerBase.RegisterEvent(System.Object,System.String)">
            <exception cref="T:System.ArgumentException">Could not find eventName on the Target.</exception>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.ExceptionStringTable">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.CallMethodActionValidMethodNotFoundExceptionMessage">
            <summary>
              Looks up a localized string similar to Could not find method named &apos;{0}&apos; on object of type &apos;{1}&apos; that matches the expected signature..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.CannotHostBehaviorCollectionMultipleTimesExceptionMessage">
            <summary>
              Looks up a localized string similar to Cannot set the same BehaviorCollection on multiple objects..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.CannotHostBehaviorMultipleTimesExceptionMessage">
            <summary>
              Looks up a localized string similar to An instance of a Behavior cannot be attached to more than one object at a time..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.CannotHostTriggerActionMultipleTimesExceptionMessage">
            <summary>
              Looks up a localized string similar to Cannot host an instance of a TriggerAction in multiple TriggerCollections simultaneously. Remove it from one TriggerCollection before adding it to another..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.CannotHostTriggerCollectionMultipleTimesExceptionMessage">
            <summary>
              Looks up a localized string similar to Cannot set the same TriggerCollection on multiple objects..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.CannotHostTriggerMultipleTimesExceptionMessage">
            <summary>
              Looks up a localized string similar to An instance of a trigger cannot be attached to more than one object at a time..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.ChangePropertyActionAmbiguousAdditionOperationExceptionMessage">
            <summary>
              Looks up a localized string similar to More than one potential addition operator was found on type &apos;{0}&apos;..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.ChangePropertyActionCannotAnimateTargetTypeExceptionMessage">
            <summary>
              Looks up a localized string similar to Cannot animate a property change on a type &apos;{0}&apos; Target. Property changes can only be animated on types derived from DependencyObject..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.ChangePropertyActionCannotFindPropertyNameExceptionMessage">
            <summary>
              Looks up a localized string similar to Cannot find a property named &quot;{0}&quot; on type &quot;{1}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.ChangePropertyActionCannotIncrementAnimatedPropertyChangeExceptionMessage">
            <summary>
              Looks up a localized string similar to The Increment property cannot be set to True if the Duration property is set..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.ChangePropertyActionCannotIncrementWriteOnlyPropertyExceptionMessage">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; property cannot be incremented because its value cannot be read..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.ChangePropertyActionCannotSetValueExceptionMessage">
            <summary>
              Looks up a localized string similar to Cannot assign value of type &quot;{0}&quot; to property &quot;{1}&quot; of type &quot;{2}&quot;. The &quot;{1}&quot; property can be assigned only values of type &quot;{2}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.ChangePropertyActionPropertyIsReadOnlyExceptionMessage">
            <summary>
              Looks up a localized string similar to Property &quot;{0}&quot; defined by type &quot;{1}&quot; does not expose a set method and therefore cannot be modified..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.CommandDoesNotExistOnBehaviorWarningMessage">
            <summary>
              Looks up a localized string similar to The command &quot;{0}&quot; does not exist or is not publicly exposed on {1}..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.DataStateBehaviorStateNameNotFoundExceptionMessage">
            <summary>
              Looks up a localized string similar to Cannot find state named &apos;{0}&apos; on type &apos;{1}&apos;. Ensure that the state exists and that it can be accessed from this context..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.DefaultTriggerAttributeInvalidTriggerTypeSpecifiedExceptionMessage">
            <summary>
              Looks up a localized string similar to &quot;{0}&quot; is not a valid type for the TriggerType parameter. Make sure &quot;{0}&quot; derives from TriggerBase..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.DuplicateItemInCollectionExceptionMessage">
            <summary>
              Looks up a localized string similar to Cannot add the same instance of &quot;{0}&quot; to a &quot;{1}&quot; more than once..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.EventTriggerBaseInvalidEventExceptionMessage">
            <summary>
              Looks up a localized string similar to The event &quot;{0}&quot; on type &quot;{1}&quot; has an incompatible signature. Make sure the event is public and satisfies the EventHandler delegate..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.EventTriggerCannotFindEventNameExceptionMessage">
            <summary>
              Looks up a localized string similar to Cannot find an event named &quot;{0}&quot; on type &quot;{1}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.GoToStateActionTargetHasNoStateGroups">
            <summary>
              Looks up a localized string similar to Target {0} does not define any VisualStateGroups. .
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.InvalidLeftOperand">
            <summary>
              Looks up a localized string similar to LeftOperand of type &quot;{0}&quot; cannot be used with operator &quot;{1}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.InvalidOperands">
            <summary>
              Looks up a localized string similar to LeftOperand of type &quot;{1}&quot; and RightOperand of type &quot;{0}&quot; cannot be used with operator &quot;{2}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.InvalidRightOperand">
            <summary>
              Looks up a localized string similar to RightOperand of type &quot;{0}&quot; cannot be used with operator &quot;{1}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.RetargetedTypeConstraintViolatedExceptionMessage">
            <summary>
              Looks up a localized string similar to An object of type &quot;{0}&quot; cannot have a {3} property of type &quot;{1}&quot;. Instances of type &quot;{0}&quot; can have only a {3} property of type &quot;{2}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.TypeConstraintViolatedExceptionMessage">
            <summary>
              Looks up a localized string similar to Cannot attach type &quot;{0}&quot; to type &quot;{1}&quot;. Instances of type &quot;{0}&quot; can only be attached to objects of type &quot;{2}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.UnableToResolveTargetNameWarningMessage">
            <summary>
              Looks up a localized string similar to Unable to resolve TargetName &quot;{0}&quot;..
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.ExceptionStringTable.UnsupportedRemoveTargetExceptionMessage">
            <summary>
              Looks up a localized string similar to The target of the RemoveElementAction is not supported..
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.IAttachedObject">
            <summary>
            An interface for an object that can be attached to another object.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.IAttachedObject.AssociatedObject">
            <summary>
            Gets the associated object.
            </summary>
            <value>The associated object.</value>
            <remarks>Represents the object the instance is attached to.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.IAttachedObject.Attach(System.Windows.DependencyObject)">
            <summary>
            Attaches to the specified object.
            </summary>
            <param name="dependencyObject">The object to attach to.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.IAttachedObject.Detach">
            <summary>
            Detaches this instance from its associated object.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Input.KeyTrigger">
            <summary>
            A Trigger that is triggered by a keyboard event.  If the target Key and Modifiers are detected, it fires.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Input.KeyTrigger.Key">
            <summary>
            The key that must be pressed for the trigger to fire.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Input.KeyTrigger.Modifiers">
            <summary>
            The modifiers that must be active for the trigger to fire (the default is no modifiers pressed).
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Input.KeyTrigger.ActiveOnFocus">
            <summary>
            If true, the Trigger only listens to its trigger Source object, which means that element must have focus for the trigger to fire.
            If false, the Trigger listens at the root, so any unhandled KeyDown/Up messages will be caught.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Input.KeyTrigger.FiredOn">
            <summary>
            Determines whether or not to listen to the KeyDown or KeyUp event.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Input.TranslateZoomRotateBehavior">
            <summary>
            Allows the user to use common touch gestures to translate, zoom, and rotate the attached object.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Input.TranslateZoomRotateBehavior.SupportedGestures">
            <summary>
            Gets or sets a value specifying which zooming and translation variants to support.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Input.TranslateZoomRotateBehavior.TranslateFriction">
            <summary>
            Gets or sets a number describing the rate at which the translation will decrease.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Input.TranslateZoomRotateBehavior.RotationalFriction">
            <summary>
            Gets or sets a number describing the rate at which the rotation will decrease.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Input.TranslateZoomRotateBehavior.ConstrainToParentBounds">
            <summary>
            Gets or sets the value indicating whether the zoom and translate position of the attached object is limited by the bounds of the parent object.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Input.TranslateZoomRotateBehavior.MinimumScale">
            <summary>
            Gets or sets a number indicating the minimum zoom value allowed.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Input.TranslateZoomRotateBehavior.MaximumScale">
            <summary>
            Gets or sets a number indicating the maximum zoom value allowed.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Input.TranslateZoomRotateBehavior.OnAttached">
            <summary>
            Called after the behavior is attached to an AssociatedObject.
            </summary>
            <remarks>Override this to hook up functionality to the AssociatedObject.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Input.TranslateZoomRotateBehavior.OnDetaching">
            <summary>
            Called when the behavior is getting detached from its AssociatedObject, but before it has actually occurred.
            </summary>
            <remarks>Override this to unhook functionality from the AssociatedObject.</remarks>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Interaction">
            <summary>
            Static class that owns the Triggers and Behaviors attached properties. Handles propagation of AssociatedObject change notifications.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Interaction.ShouldRunInDesignMode">
            <summary>
            Gets or sets a value indicating whether to run as if in design mode.
            </summary>
            <value>
            	<c>True</c> if [should run in design mode]; otherwise, <c>False</c>.
            </value>
            <remarks>Not to be used outside unit tests.</remarks>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Interaction.TriggersProperty">
            <summary>
            This property is used as the internal backing store for the public Triggers attached property.
            </summary>
            <remarks>
            This property is not exposed publicly. This forces clients to use the GetTriggers and SetTriggers methods to access the
            collection, ensuring the collection exists and is set before it is used.
            </remarks>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Interaction.BehaviorsProperty">
            <summary>
            This property is used as the internal backing store for the public Behaviors attached property.
            </summary>
            <remarks>
            This property is not exposed publicly. This forces clients to use the GetBehaviors and SetBehaviors methods to access the
            collection, ensuring the collection exists and is set before it is used.
            </remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Interaction.GetTriggers(System.Windows.DependencyObject)">
            <summary>
            Gets the TriggerCollection containing the triggers associated with the specified object.
            </summary>
            <param name="obj">The object from which to retrieve the triggers.</param>
            <returns>A TriggerCollection containing the triggers associated with the specified object.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Interaction.GetBehaviors(System.Windows.DependencyObject)">
            <summary>
            Gets the <see cref="T:Microsoft.Xaml.Behaviors.BehaviorCollection"/> associated with a specified object.
            </summary>
            <param name="obj">The object from which to retrieve the <see cref="T:Microsoft.Xaml.Behaviors.BehaviorCollection"/>.</param>
            <returns>A <see cref="T:Microsoft.Xaml.Behaviors.BehaviorCollection"/> containing the behaviors associated with the specified object.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Interaction.OnBehaviorsChanged(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <exception cref="T:System.InvalidOperationException">Cannot host the same BehaviorCollection on more than one object at a time.</exception>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Interaction.OnTriggersChanged(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <exception cref="T:System.InvalidOperationException">Cannot host the same TriggerCollection on more than one object at a time.</exception>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Interaction.IsElementLoaded(System.Windows.FrameworkElement)">
            <summary>
            A helper function to take the place of FrameworkElement.IsLoaded, as this property is not available in Silverlight.
            </summary>
            <param name="element">The element of interest.</param>
            <returns>True if the element has been loaded; otherwise, False.</returns>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.InvokeCommandAction">
            <summary>
            Executes a specified ICommand when invoked.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.CommandName">
            <summary>
            Gets or sets the name of the command this action should invoke.
            </summary>
            <value>The name of the command this action should invoke.</value>
            <remarks>This property will be superseded by the Command property if both are set.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.Command">
            <summary>
            Gets or sets the command this action should invoke. This is a dependency property.
            </summary>
            <value>The command to execute.</value>
            <remarks>This property will take precedence over the CommandName property if both are set.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.CommandParameter">
            <summary>
            Gets or sets the command parameter. This is a dependency property.
            </summary>
            <value>The command parameter.</value>
            <remarks>This is the value passed to ICommand.CanExecute and ICommand.Execute.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.EventArgsConverter">
            <summary>
            Gets or sets the IValueConverter that is used to convert the EventArgs passed to the Command as a parameter.
            </summary>
            <remarks>If the <see cref="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.Command"/> or <see cref="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.EventArgsParameterPath"/> properties are set, this property is ignored.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.EventArgsConverterParameter">
            <summary>
            Gets or sets the parameter that is passed to the EventArgsConverter.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.EventArgsParameterPath">
            <summary>
            Gets or sets the parameter path used to extract a value from an <see cref= "T:System.EventArgs" /> property to pass to the Command as a parameter.
            </summary>
            <remarks>If the <see cref="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.Command"/> propert is set, this property is ignored.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.PassEventArgsToCommand">
            <summary>
            Specifies whether the EventArgs of the event that triggered this action should be passed to the Command as a parameter.
            </summary>
            <remarks>If the <see cref="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.Command"/>, <see cref="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.EventArgsParameterPath"/>, or <see cref="P:Microsoft.Xaml.Behaviors.InvokeCommandAction.EventArgsConverter"/> properties are set, this property is ignored.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.InvokeCommandAction.Invoke(System.Object)">
            <summary>
            Invokes the action.
            </summary>
            <param name="parameter">The parameter to the action. If the action does not require a parameter, the parameter may be set to a null reference.</param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Layout.FluidMoveScope">
            <summary>
            This enumerated type indicates whether a FluidMoveBehavior applies to the element to which it is attached, or to the children of that element.
            "Self" is useful when there is a single element that should behave in a special manner; "Children" is useful when the same behavior should apply to all
            children of a WrapPanel or to the ItemsHost panel of an ItemsControl.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Layout.TagType">
            <summary>
            This enumerated type indicates whether an element is identified by itself, or by its DataContext.
            DataContext identification allows movement from one data-driven location to another.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehaviorBase.AppliesTo">
            <summary>
            Indicates whether the behavior applies just to this element, or to all children of the element (if the element is a Panel).
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehaviorBase.AppliesToProperty">
            <summary>
            Dependency property for the scope of the behavior. See FluidMoveScope for more details.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehaviorBase.IsActive">
            <summary>
            Indicates whether the behavior is currently active.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehaviorBase.IsActiveProperty">
            <summary>
            Dependency property for the active state of the behavior.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehaviorBase.Tag">
            <summary>
            Indicates whether to use the element as its own tag, or to use the binding on the element as the tag.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehaviorBase.TagProperty">
            <summary>
            Dependency property that provides the ability to use the element as its own tag, or the binding on the element.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehaviorBase.TagPath">
            <summary>
            Extra path to add to the binding when TagType is specified.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehaviorBase.TagPathProperty">
            <summary>
            Dependency property for the extra path to add to the binding when UsaBindingAsTag is true.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehaviorBase.IdentityTagProperty">
            <summary>
            Identity tag used to detect element motion between containers.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehaviorBase.TagData">
            <summary>
            Private structure that stores all relevant data pertaining to a tagged item.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior">
            <summary>
            Behavior that watches an element (or a set of elements) for layout changes, and moves the element smoothly to the new position when needed.
            This behavior does not animate the size or visibility of an element; it only animates the offset of that element within its parent container.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.Duration">
            <summary>
            The duration of the move.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.DurationProperty">
            <summary>
            Dependency property for the duration of the move.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.InitialTag">
            <summary>
            Spawning point for this item.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.InitialTagProperty">
            <summary>
            Dependency property for the tag type to use just before the object is loaded.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.InitialTagPath">
            <summary>
            Extra path to add to the binding when TagType is specified.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.InitialTagPathProperty">
            <summary>
            Dependency property for the extra path to add to the binding when UsaBindingAsTag is true.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.initialIdentityTagProperty">
            <summary>
            Identity tag used to detect element motion between containers.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.FloatAbove">
            <summary>
            Flag that says whether elements are allowed to float above their containers (in a Popup or Adorner) when changing containers.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.FloatAboveProperty">
            <summary>
            Dependency property for the FloatAbove flag.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.EaseX">
            <summary>
            EasingFunction to use for the horizontal component of the move.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.EaseXProperty">
            <summary>
            Dependency property for the EasingFunction to use for the horizontal component of the move.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.EaseY">
            <summary>
            EasingFunction to use for the vertical component of the move.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.EaseYProperty">
            <summary>
            Dependency property for the EasingFunction to use for the vertical component of the move.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.overlayProperty">
            <summary>
            Remember the popup/adorner being used, in case of element motion between containers when FloatAbove is true.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.cacheDuringOverlayProperty">
            <summary>
            Opacity cache used when floating a Popup.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.FluidMoveBehavior.hasTransformWrapperProperty">
            <summary>
            Marks the animation transform.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Layout.AdornerContainer">
            <summary>
            Simple helper class to allow any UIElements to be used as an Adorner.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior">
            <summary>
            Repositions the attached element in response to mouse drag gestures on the element.
            </summary>
        </member>
        <member name="E:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.DragBegun">
            <summary>
            Occurs when a drag gesture is initiated.
            </summary>
        </member>
        <member name="E:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.Dragging">
            <summary>
            Occurs when a drag gesture update is processed.
            </summary>
        </member>
        <member name="E:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.DragFinished">
            <summary>
            Occurs when a drag gesture is finished.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.XProperty">
            <summary>
            Dependency property for the X position of the dragged element, relative to the left of the root element.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.YProperty">
            <summary>
            Dependency property for the Y position of the dragged element, relative to the top of the root element.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.ConstrainToParentBoundsProperty">
            <summary>
            Dependency property for the ConstrainToParentBounds property. If true, the dragged element will be constrained to stay within the bounds of its parent container.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.X">
            <summary>
            Gets or sets the X position of the dragged element, relative to the left of the root element. This is a dependency property.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.Y">
            <summary>
            Gets or sets the Y position of the dragged element, relative to the top of the root element. This is a dependency property.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.ConstrainToParentBounds">
            <summary>
            Gets or sets a value indicating whether the dragged element is constrained to stay within the bounds of its parent container. This is a dependency property.
            </summary>
            <value>
            	<c>True</c> if the dragged element should be constrained to its parents bounds; otherwise, <c>False</c>.
            </value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.ActualPosition">
            <summary>
            Gets the on-screen position of the associated element in root coordinates.
            </summary>
            <value>The on-screen position of the associated element in root coordinates.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.ElementBounds">
            <summary>
            Gets the element bounds in element coordinates.
            </summary>
            <value>The element bounds in element coordinates.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.ParentElement">
            <summary>
            Gets the parent element of the associated object.
            </summary>
            <value>The parent element of the associated object.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.RootElement">
            <summary>
            Gets the root element of the scene in which the associated object is located.
            </summary>
            <value>The root element of the scene in which the associated object is located.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.RenderTransform">
            <summary>
            Gets and sets the RenderTransform of the associated element.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.UpdatePosition(System.Windows.Point)">
            <summary>
            Attempts to update the position of the associated element to the specified coordinates.
            </summary>
            <param name="point">The desired position of the element in root coordinates.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.ApplyTranslation(System.Double,System.Double)">
            <summary>
            Applies a relative position translation to the associated element.
            </summary>
            <param name="x">The X component of the desired translation in root coordinates.</param>
            <param name="y">The Y component of the desired translation in root coordinates.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.ApplyTranslationTransform(System.Double,System.Double)">
            <summary>
            Applies the given translation to the RenderTransform of the associated element.
            </summary>
            <param name="x">The X component of the translation in parent coordinates.</param>
            <param name="y">The Y component of the translation in parent coordinates.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.CloneTransform(System.Windows.Media.Transform)">
            <summary>
            Does a recursive deep copy of the specified transform.
            </summary>
            <param name="transform">The transform to clone.</param>
            <returns>A deep copy of the specified transform, or null if the specified transform is null.</returns>
            <exception cref="T:System.ArgumentException">Thrown if the type of the Transform is not recognized.</exception>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.UpdatePosition">
            <summary>
            Updates the X and Y properties based on the current rendered position of the associated element.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.RectContainsRect(System.Windows.Rect,System.Windows.Rect)">
            <summary>
            Check if one Rect is contained by another.
            </summary>
            <param name="rect1">The containing Rect.</param>
            <param name="rect2">The contained Rect.</param>
            <returns><c>True</c> if rect1 contains rect2; otherwise, <c>False</c>.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.TransformAsVector(System.Windows.Media.GeneralTransform,System.Double,System.Double)">
            <summary>
            Transforms as vector.
            </summary>
            <param name="transform">The transform.</param>
            <param name="x">The X component of the vector.</param>
            <param name="y">The Y component of the vector.</param>
            <returns>A point containing the values of X and Y transformed by transform as a vector.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.GetTransformOffset(System.Windows.Media.GeneralTransform)">
            <summary>
            Gets the transform offset.
            </summary>
            <param name="transform">The transform.</param>
            <returns>The offset of the transform.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.OnAttached">
            <summary>
            Called after the behavior is attached to an AssociatedObject.
            </summary>
            <remarks>Override this to hook up functionality to the AssociatedObject.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Layout.MouseDragElementBehavior.OnDetaching">
            <summary>
            Called when the behavior is getting detached from its AssociatedObject, but before it has actually occurred.
            </summary>
            <remarks>Override this to unhook functionality from the AssociatedObject.</remarks>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Media.PlaySoundAction">
            <summary>
            An action that will play a sound to completion.
            </summary>
            <remarks>
            This action is intended for use with short sound effects that don't need to be stopped or controlled. If you're trying 
            to create a music player or game, it may not meet your needs.
            </remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Media.PlaySoundAction.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.Media.PlaySoundAction"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Media.PlaySoundAction.Source">
            <summary>
            A Uri defining the location of the sound file. This is used to set the source property of the MediaElement. This is a dependency property.
            </summary>
            <remarks>
            The sound can be any file format supported by MediaElement. In the case of a video, it will play only the
            audio portion.
            </remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Media.PlaySoundAction.Volume">
            <summary>
            Control the volume of the sound. This is used to set the Volume property of the MediaElement. This is a dependency property.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Media.PlaySoundAction.SetMediaElementProperties(System.Windows.Controls.MediaElement)">
            <summary>
            When the action is invoked, this method is used to customize the dynamically created MediaElement.
            </summary>
            <remarks>
            This method may be useful for Action authors who wish to extend PlaySoundAction. If you want to control the 
            MediaElement Balance property, you could inherit from PlaySoundAction and override this method.
            </remarks>
            <param name="mediaElement"></param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Media.PlaySoundAction.Invoke(System.Object)">
            <summary>
            This method is called when some criteria are met and the action should be invoked. 
            </summary>
            <remarks>
            Each invocation of the Action plays a new sound. Although the implementation is subject-to-change, the caller should 
            anticipate that this will create a new MediaElement that will be cleaned up when the sound completes or if the media 
            fails to play.
            </remarks>
            <param name="parameter"></param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Media.StoryboardAction">
            <summary>
            An abstract class that provides the ability to target a Storyboard.
            </summary>
            <remarks>
            For action authors, this class provides a standard way to target a Storyboard. Design tools may choose to provide a 
            special editing experience for classes that inherit from this action, thereby improving the designer experience.
            </remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Media.StoryboardAction.Storyboard">
            <summary>
            The targeted Storyboard. This is a dependency property.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Media.StoryboardAction.OnStoryboardChanged(System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            This method is called when the Storyboard property is changed.
            </summary>
            <param name="args"></param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Media.ControlStoryboardAction">
            <summary>
            An action that will change the state of a targeted storyboard when invoked.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Media.ControlStoryboardAction.Invoke(System.Object)">
            <summary>
            This method is called when some criteria is met and the action should be invoked. This method will attempt to 
            change the targeted storyboard in a way defined by the ControlStoryboardOption.
            </summary>
            <param name="parameter"></param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Media.StoryboardTrigger">
            <summary>
            An abstract class that provides the ability to target a Storyboard.
            </summary>
            <remarks>
            For Trigger authors, this class provides a standard way to target a Storyboard. Design tools may choose to provide a 
            special editing experience for classes that inherit from this trigger, thereby improving the designer experience. 
            </remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Media.StoryboardTrigger.Storyboard">
            <summary>
            The targeted Storyboard. This is a dependency property.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Media.StoryboardTrigger.OnStoryboardChanged(System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            This method is called when the Storyboard property is changed.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Media.StoryboardCompletedTrigger">
            <summary>
            A trigger that listens for the completion of a Storyboard.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Media.StoryboardCompletedTrigger.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.Media.StoryboardCompletedTrigger"/> class.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.Media.TransitionEffect">
            <summary>
            Defines a transition effect shader that transitions from one visual to another visual
            using an interpolated value between 0 and 1.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Media.TransitionEffect.InputProperty">
            <summary>
            Brush-valued properties that turn into sampler-properties in the shader.
            Represents the image present in the final state of the transition.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Media.TransitionEffect.OldImageProperty">
            <summary>
            Brush-valued properties that turn into sampler-properties in the shader.
            Represents the image present in the initial state of the transition.
            </summary>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Media.TransitionEffect.ProgressProperty">
            <summary>
            A Dependency property as the backing store for Progress.
            Also used to represent the state of a transition from start to finish (range between 0 and 1).
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Media.TransitionEffect.CloneCurrentValue">
            <summary>
            Creates a modifiable clone (deep copy) of the <see cref="T:TransitionEffect"/> using its current values.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Media.TransitionEffect.DeepCopy">
            <summary>
            Makes a deep copy of the transition effect. Implements CloneCurrentValue in Silverlight.
            </summary>
            <returns>A clone of current instance of transition effect.</returns>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.Media.TransitionEffect.#ctor">
            <summary>
            Updates the shader's variables to the default values.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Media.TransitionEffect.Input">
            <summary>
            Gets or sets the Input variable within the shader.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Media.TransitionEffect.OldImage">
            <summary>
            Gets or sets the OldImage variable within the shader.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.Media.TransitionEffect.Progress">
            <summary>
            Gets or sets the Progress variable within the shader.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.NameResolvedEventArgs">
            <summary>
            Provides data about which objects were affected when resolving a name change.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.NameResolver">
            <summary>
            Helper class to handle the logic of resolving a TargetName into a Target element
            based on the context provided by a host element.
            </summary>
        </member>
        <member name="E:Microsoft.Xaml.Behaviors.NameResolver.ResolvedElementChanged">
            <summary>
            Occurs when the resolved element has changed.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.NameResolver.Name">
            <summary>
            Gets or sets the name of the element to attempt to resolve.
            </summary>
            <value>The name to attempt to resolve.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.NameResolver.Object">
            <summary>
            The resolved object. Will return the reference element if TargetName is null or empty, or if a resolve has not been attempted.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.NameResolver.NameScopeReferenceElement">
            <summary>
            Gets or sets the reference element from which to perform the name resolution.
            </summary>
            <value>The reference element.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.NameResolver.PendingReferenceElementLoad">
            <summary>
            Gets or sets a value indicating whether the reference element load is pending.
            </summary>
            <value>
            	<c>True</c> if [pending reference element load]; otherwise, <c>False</c>.
            </value>
            <remarks>
            If the Host has not been loaded, the name will not be resolved.
            In that case, delay the resolution and track that fact with this property.
            </remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.NameResolver.UpdateObjectFromName(System.Windows.DependencyObject)">
            <summary>
            Attempts to update the resolved object from the name within the context of the namescope reference element.
            </summary>
            <param name="oldObject">The old resolved object.</param>
            <remarks>
            Resets the existing target and attempts to resolve the current TargetName from the
            context of the current Host. If it cannot resolve from the context of the Host, it will
            continue up the visual tree until it resolves. If it has not resolved it when it reaches
            the root, it will set the Target to null and write a warning message to Debug output.
            </remarks>
        </member>
        <member name="F:Microsoft.Xaml.Behaviors.Serializer.Data.CurrentSchemaVersion">
            <summary>
            The current version of the flow file schema.
            This number should be incremented whenever:
            	A new _required_ field is added.
            	The data type of a field is changed.
            	The semantic interpretation of a field is changed.
            	
            When upgrading the current schema number, you'll also need to take into account
            migration/upgrade strategies, and mechanisms for deserializing older schemas.
            In some cases, the same serializer data structure may suffice by applying different
            parsing validation rules.  In other cases, a new data structure may be needed to
            deserialize the old format from disk.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.TargetedTriggerAction`1">
            <summary>
            Represents an action that can be targeted to affect an object other than its AssociatedObject.
            </summary>
            <typeparam name="T">The type constraint on the target.</typeparam>
            <remarks>
            	TargetedTriggerAction extends TriggerAction to add knowledge of another element than the one it is attached to. 
            	This allows a user to invoke the action on an element other than the one it is attached to in response to a 
            	trigger firing. Override OnTargetChanged to hook or unhook handlers on the target element, and OnAttached/OnDetaching 
            	for the associated element. The type of the Target element can be constrained by the generic type parameter. If 
            	you need control over the type of the AssociatedObject, set a TypeConstraintAttribute on your derived type.
            </remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TargetedTriggerAction`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.TargetedTriggerAction`1"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TargetedTriggerAction`1.Target">
            <summary>
            Gets the target object. If TargetName is not set or cannot be resolved, defaults to the AssociatedObject.
            </summary>
            <value>The target.</value>
            <remarks>In general, this property should be used in place of AssociatedObject in derived classes.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TargetedTriggerAction`1.OnTargetChanged(`0,`0)">
            <summary>
            Called when the target property changes.
            </summary>
            <remarks>Override this to hook and unhook functionality on the specified Target, rather than the AssociatedObject.</remarks>
            <param name="oldTarget">The old target.</param>
            <param name="newTarget">The new target.</param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.TargetedTriggerAction">
            <summary>
            Represents an action that can be targeted to affect an object other than its AssociatedObject.
            </summary>
            <remarks>This is an infrastructure class. Action authors should derive from TargetedTriggerAction&lt;T&gt; instead of this class.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TargetedTriggerAction.TargetObject">
            <summary>
            Gets or sets the target object. If TargetObject is not set, the target will look for the object specified by TargetName. If an element referred to by TargetName cannot be found, the target will default to the AssociatedObject. This is a dependency property.
            </summary>
            <value>The target object.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TargetedTriggerAction.TargetName">
            <summary>
            Gets or sets the name of the object this action targets. If Target is set, this property is ignored. If Target is not set and TargetName is not set or cannot be resolved, the target will default to the AssociatedObject. This is a dependency property.
            </summary>
            <value>The name of the target object.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TargetedTriggerAction.Target">
            <summary>
            Gets the target object. If TargetObject is set, returns TargetObject. Else, if TargetName is not set or cannot be resolved, defaults to the AssociatedObject.
            </summary>
            <value>The target object.</value>
            <remarks>In general, this property should be used in place of AssociatedObject in derived classes.</remarks>
            <exception cref="T:System.InvalidOperationException">The Target element does not satisfy the type constraint.</exception>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TargetedTriggerAction.AssociatedObjectTypeConstraint">
            <summary>
            Gets the associated object type constraint.
            </summary>
            <value>The associated object type constraint.</value>
            <remarks>Define a TypeConstraintAttribute on a derived type to constrain the types it may be attached to.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TargetedTriggerAction.TargetTypeConstraint">
            <summary>
            Gets the target type constraint.
            </summary>
            <value>The target type constraint.</value>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TargetedTriggerAction.OnTargetChangedImpl(System.Object,System.Object)">
            <summary>
            Called when the target changes.
            </summary>
            <param name="oldTarget">The old target.</param>
            <param name="newTarget">The new target.</param>
            <remarks>This function should be overriden in derived classes to hook and unhook functionality from the changing source objects.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TargetedTriggerAction.OnAttached">
            <summary>
            Called after the action is attached to an AssociatedObject.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TargetedTriggerAction.OnDetaching">
            <summary>
            Called when the action is being detached from its AssociatedObject, but before it has actually occurred.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.TriggerAction`1">
            <summary>
            Represents an attachable object that encapsulates a unit of functionality.
            </summary>
            <typeparam name="T">The type to which this action can be attached.</typeparam>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerAction`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.TriggerAction`1"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerAction`1.AssociatedObject">
            <summary>
            Gets the object to which this <see cref="T:Microsoft.Xaml.Behaviors.TriggerAction`1"/> is attached.
            </summary>
            <value>The associated object.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerAction`1.AssociatedObjectTypeConstraint">
            <summary>
            Gets the associated object type constraint.
            </summary>
            <value>The associated object type constraint.</value>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.TriggerAction">
            <summary>
            Represents an attachable object that encapsulates a unit of functionality.
            </summary>
            <remarks>This is an infrastructure class. Action authors should derive from TriggerAction&lt;T&gt; instead of this class.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerAction.IsEnabled">
            <summary>
            Gets or sets a value indicating whether this action will run when invoked. This is a dependency property.
            </summary>
            <value>
            	<c>True</c> if this action will be run when invoked; otherwise, <c>False</c>.
            </value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerAction.AssociatedObject">
            <summary>
            Gets the object to which this action is attached.
            </summary>
            <value>The associated object.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerAction.AssociatedObjectTypeConstraint">
            <summary>
            Gets the associated object type constraint.
            </summary>
            <value>The associated object type constraint.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerAction.IsHosted">
            <summary>
            Gets or sets a value indicating whether this instance is attached.
            </summary>
            <value><c>True</c> if this instance is attached; otherwise, <c>False</c>.</value>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerAction.CallInvoke(System.Object)">
            <summary>
            Attempts to invoke the action.
            </summary>
            <param name="parameter">The parameter to the action. If the action does not require a parameter, the parameter may be set to a null reference.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerAction.Invoke(System.Object)">
            <summary>
            Invokes the action.
            </summary>
            <param name="parameter">The parameter to the action. If the action does not require a parameter, the parameter may be set to a null reference.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerAction.OnAttached">
            <summary>
            Called after the action is attached to an AssociatedObject.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerAction.OnDetaching">
            <summary>
            Called when the action is being detached from its AssociatedObject, but before it has actually occurred.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerAction.CreateInstanceCore">
            <summary>
            When implemented in a derived class, creates a new instance of the <see cref="T:System.Windows.Freezable"/> derived class.
            </summary>
            <returns>The new instance.</returns>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerAction.Microsoft#Xaml#Behaviors#IAttachedObject#AssociatedObject">
            <summary>
            Gets the associated object.
            </summary>
            <value>The associated object.</value>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerAction.Attach(System.Windows.DependencyObject)">
            <summary>
            Attaches to the specified object.
            </summary>
            <param name="dependencyObject">The object to attach to.</param>
            <exception cref="T:System.InvalidOperationException">Cannot host the same TriggerAction on more than one object at a time.</exception>
            <exception cref="T:System.InvalidOperationException">dependencyObject does not satisfy the TriggerAction type constraint.</exception>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerAction.Detach">
            <summary>
            Detaches this instance from its associated object.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.TriggerActionCollection">
            <summary>
            Represents a collection of actions with a shared AssociatedObject and provides change notifications to its contents when that AssociatedObject changes.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerActionCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.TriggerActionCollection"/> class.
            </summary>
            <remarks>Internal, because this should not be inherited outside this assembly.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerActionCollection.OnAttached">
            <summary>
            Called immediately after the collection is attached to an AssociatedObject.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerActionCollection.OnDetaching">
            <summary>
            Called when the collection is being detached from its AssociatedObject, but before it has actually occurred.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerActionCollection.ItemAdded(Microsoft.Xaml.Behaviors.TriggerAction)">
            <summary>
            Called when a new item is added to the collection.
            </summary>
            <param name="item">The new item.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerActionCollection.ItemRemoved(Microsoft.Xaml.Behaviors.TriggerAction)">
            <summary>
            Called when an item is removed from the collection.
            </summary>
            <param name="item">The removed item.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerActionCollection.CreateInstanceCore">
            <summary>
            Creates a new instance of the TriggerActionCollection.
            </summary>
            <returns>The new instance.</returns>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.TriggerBase`1">
            <summary>
            Represents an object that can invoke actions conditionally.
            </summary>
            <typeparam name="T">The type to which this trigger can be attached.</typeparam>
            <remarks>
            	TriggerBase is the base class for controlling actions. Override OnAttached() and 
            	OnDetaching() to hook and unhook handlers on the AssociatedObject. You may 
            	constrain the types that a derived TriggerBase may be attached to by specifying 
            	the generic parameter. Call InvokeActions() to fire all Actions associated with 
            	this TriggerBase.
            </remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerBase`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.TriggerBase`1"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerBase`1.AssociatedObject">
            <summary>
            Gets the object to which the trigger is attached.
            </summary>
            <value>The associated object.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerBase`1.AssociatedObjectTypeConstraint">
            <summary>
            Gets the type constraint of the associated object.
            </summary>
            <value>The associated object type constraint.</value>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.PreviewInvokeEventArgs">
            <summary>
            Argument passed to PreviewInvoke event. Assigning Cancelling to True will cancel the invoking of the trigger.
            </summary>
            <remarks>This is an infrastructure class. Behavior attached to a trigger base object can add its behavior as a listener to TriggerBase.PreviewInvoke.</remarks>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.TriggerBase">
            <summary>
            Represents an object that can invoke Actions conditionally.
            </summary>
            <remarks>This is an infrastructure class. Trigger authors should derive from Trigger&lt;T&gt; instead of this class.</remarks>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerBase.AssociatedObject">
            <summary>
            Gets the object to which the trigger is attached.
            </summary>
            <value>The associated object.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerBase.AssociatedObjectTypeConstraint">
            <summary>
            Gets the type constraint of the associated object.
            </summary>
            <value>The associated object type constraint.</value>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerBase.Actions">
            <summary>
            Gets the actions associated with this trigger.
            </summary>
            <value>The actions associated with this trigger.</value>
        </member>
        <member name="E:Microsoft.Xaml.Behaviors.TriggerBase.PreviewInvoke">
            <summary>
            Event handler for registering to PreviewInvoke.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerBase.InvokeActions(System.Object)">
            <summary>
            Invoke all actions associated with this trigger.
            </summary>
            <remarks>Derived classes should call this to fire the trigger.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerBase.OnAttached">
            <summary>
            Called after the trigger is attached to an AssociatedObject.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerBase.OnDetaching">
            <summary>
            Called when the trigger is being detached from its AssociatedObject, but before it has actually occurred.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerBase.CreateInstanceCore">
            <summary>
            Creates a new instance of the TriggerBase derived class.
            </summary>
            <returns>The new instance.</returns>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TriggerBase.Microsoft#Xaml#Behaviors#IAttachedObject#AssociatedObject">
            <summary>
            Gets the associated object.
            </summary>
            <value>The associated object.</value>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerBase.Attach(System.Windows.DependencyObject)">
            <summary>
            Attaches to the specified object.
            </summary>
            <param name="dependencyObject">The object to attach to.</param>
            <exception cref="T:System.InvalidOperationException">Cannot host the same trigger on more than one object at a time.</exception>
            <exception cref="T:System.InvalidOperationException">dependencyObject does not satisfy the trigger type constraint.</exception>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerBase.Detach">
            <summary>
            Detaches this instance from its associated object.
            </summary>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.TriggerCollection">
            <summary>
             Represents a collection of triggers with a shared AssociatedObject and provides change notifications to its contents when that AssociatedObject changes.
             </summary>
             <remarks>Sealed, because this should not be inherited outside this assembly.</remarks>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.TriggerCollection"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerCollection.OnAttached">
            <summary>
            Called immediately after the collection is attached to an AssociatedObject.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerCollection.OnDetaching">
            <summary>
            Called when the collection is being detached from its AssociatedObject, but before it has actually occurred.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerCollection.ItemAdded(Microsoft.Xaml.Behaviors.TriggerBase)">
            <summary>
            Called when a new item is added to the collection.
            </summary>
            <param name="item">The new item.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerCollection.ItemRemoved(Microsoft.Xaml.Behaviors.TriggerBase)">
            <summary>
            Called when an item is removed from the collection.
            </summary>
            <param name="item">The removed item.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TriggerCollection.CreateInstanceCore">
            <summary>
            Creates a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.TriggerCollection"/>.
            </summary>
            <returns>The new instance.</returns>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.TypeConstraintAttribute">
            <summary>
            Specifies type constraints on the AssociatedObject of TargetedTriggerAction and EventTriggerBase.
            </summary>
        </member>
        <member name="P:Microsoft.Xaml.Behaviors.TypeConstraintAttribute.Constraint">
            <summary>
            Gets the constraint type.
            </summary>
            <value>The constraint type.</value>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.TypeConstraintAttribute.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Xaml.Behaviors.TypeConstraintAttribute"/> class.
            </summary>
            <param name="constraint">The constraint type.</param>
        </member>
        <member name="T:Microsoft.Xaml.Behaviors.VisualStateUtilities">
            <summary>
            This class provides various platform agnostic standard operations for working with VisualStateManager.
            </summary>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.VisualStateUtilities.GoToState(System.Windows.FrameworkElement,System.String,System.Boolean)">
            <summary>
            Transitions the control between two states.
            </summary>
            <param name="element">The element to transition between states.</param>
            <param name="stateName">The state to transition to.</param>
            <param name="useTransitions">True to use a System.Windows.VisualTransition to transition between states; otherwise, false.</param>
            <returns>True if the control successfully transitioned to the new state; otherwise, false.</returns>
            <exception cref="T:System.ArgumentNullException">Control is null.</exception>
            <exception cref="T:System.ArgumentNullException">StateName is null.</exception>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.VisualStateUtilities.GetVisualStateGroups(System.Windows.FrameworkElement)">
            <summary>
            Gets the value of the VisualStateManager.VisualStateGroups attached property.
            </summary>
            <param name="targetObject">The element from which to get the VisualStateManager.VisualStateGroups.</param>
        </member>
        <member name="M:Microsoft.Xaml.Behaviors.VisualStateUtilities.TryFindNearestStatefulControl(System.Windows.FrameworkElement,System.Windows.FrameworkElement@)">
            <summary>
            Find the nearest parent which contains visual states.
            </summary>
            <param name="contextElement">The element from which to find the nearest stateful control.</param>
            <param name="resolvedControl">The nearest stateful control if True; else null.</param>
            <returns>True if a parent contains visual states; else False.</returns>
        </member>
    </members>
</doc>
